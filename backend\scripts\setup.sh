#!/bin/bash

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "Error: .env file not found"
    exit 1
fi

# Check if POSTGRES_DB is set
if [ -z "$POSTGRES_DB" ]; then
    echo "Error: POSTGRES_DB is not set in .env file"
    exit 1
fi

# Function to check if PostgreSQL is ready
wait_for_postgres() {
    echo "Waiting for PostgreSQL to be ready..."
    while ! docker exec kibbutz-postgres pg_isready -U postgres > /dev/null 2>&1; do
        sleep 1
    done
    echo "PostgreSQL is ready!"
}

# Function to create database if it doesn't exist
create_database() {
    echo "Checking if database $POSTGRES_DB exists..."
    if ! docker exec kibbutz-postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw $POSTGRES_DB; then
        echo "Creating database $POSTGRES_DB..."
        docker exec kibbutz-postgres psql -U postgres -c "CREATE DATABASE $POSTGRES_DB;"
        echo "Database created successfully!"
    else
        echo "Database $POSTGRES_DB already exists."
    fi
}

# Function to create auth schema
create_auth_schema() {
    echo "Creating auth schema..."
    docker exec kibbutz-postgres psql -U postgres -d $POSTGRES_DB -c "CREATE SCHEMA IF NOT EXISTS auth;"
    echo "Auth schema created successfully!"
}

# Function to create Elasticsearch and Filebeat directories
create_elastic_directories() {
    echo "Creating Elasticsearch and Filebeat directories..."
    mkdir -p ./volumes/elasticsearch
    mkdir -p ./volumes/filebeat
    echo "Directories created successfully!"
}

# Function to create food-images bucket in Supabase storage
create_food_images_bucket() {
    echo "Creating food-images bucket in Supabase storage..."
    curl -X POST "https://your-supabase-url.supabase.co/storage/v1/bucket" \
         -H "apikey: $SUPABASE_SERVICE_ROLE_KEY" \
         -H "Content-Type: application/json" \
         -d '{"name": "food-images", "public": true}'
    echo "food-images bucket created successfully!"
}

# Main execution
echo "Starting setup..."

# Create Elasticsearch and Filebeat directories
create_elastic_directories

# Wait for PostgreSQL to be ready
wait_for_postgres

# Create database
create_database

# Create auth schema
create_auth_schema

# Create food-images bucket
create_food_images_bucket

echo "Setup completed successfully!" 