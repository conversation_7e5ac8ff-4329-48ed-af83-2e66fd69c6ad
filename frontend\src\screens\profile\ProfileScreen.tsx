import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@contexts';
import { getTextAlign } from '@utils';
import { RootStackParamList } from '@navigation/types';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { HEADER_ICONS } from '@constants';

type ProfileScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Profile'>;

export default function ProfileScreen() {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const navigation = useNavigation<ProfileScreenNavigationProp>();

  const handleLogout = async () => {
    console.log('🔴 ProfileScreen logout button clicked');
    console.log('🔴 ProfileScreen logout function type:', typeof logout);
    console.log('🔴 ProfileScreen calling logout directly');

    try {
      console.log('🔴 ProfileScreen calling logout...');
      await logout();
      console.log('🔴 ProfileScreen logout completed');
    } catch (error) {
      console.error('🔴 ProfileScreen logout error:', error);
    }
  };

  const getUserInitial = () => {
    if (user?.first_name) {
      return user.first_name.charAt(0).toUpperCase();
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  return (
    <View style={styles.container}>
      <Header
        title="👤 הפרופיל שלי"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {/* User Avatar and Info */}
        <View style={styles.userSection}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>{getUserInitial()}</Text>
          </View>
          <Text style={[styles.userName, { textAlign: getTextAlign() }]}>
            {user?.first_name && user?.last_name
              ? `${user.first_name} ${user.last_name}`
              : user?.first_name || user?.email || 'משתמש'}
          </Text>
          <Text style={[styles.userRole, { textAlign: getTextAlign() }]}>
            {user?.role === 'ADMIN' ? 'מנהל עליון' :
              user?.role === 'MODERATOR' ? 'מנהל' :
              user?.role === 'USER' ?
                (user?.user_type === 'ADULT' ? 'מבוגר' :
                  user?.user_type === 'YOUTH' ? 'נוער' :
                  user?.user_type === 'CHILD' ? 'ילד' :
                  user?.user_type === 'EXTERNAL' ? 'חיצוני' : 'תושב')
                : 'ילד/נוער'
            } • הצטרף {new Date().toLocaleDateString('he-IL', { month: 'long', year: 'numeric' })}
          </Text>
        </View>

        {/* Address Section */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>📍</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>כתובת</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>
                {user?.address || 'רחוב הראשי 123, מרכז הכפר'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Contact Section */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>📞</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>פרטי קשר</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>
                {user?.phone || user?.email || 'לא הוגדר מספר טלפון'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>🔔</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>התראות</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>ניהול התראות ועדכונים</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>🔒</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>פרטיות ובטיחות</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>שליטה במי יכול לראות את המידע שלך</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>🌍</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>שפה</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>שינוי שפת האפליקציה</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>❓</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>עזרה ותמיכה</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>קבלת עזרה ויצירת קשר עם התמיכה</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => navigation.navigate('Settings')}
          >
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>⚙️</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>הגדרות תצוגה</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>התאמת נושא, צבעים וגופנים</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>📱</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>גרסת האפליקציה</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>קיבוץ אפליקציה v1.0.0</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.featureCard}>
            <View style={[styles.featureIcon, { backgroundColor: '#a55eea' }]}>
              <Text style={styles.featureIconText}>👨‍👩‍👧‍👦</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>מצב משפחה</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>ממשק ידידותי לילדים ותוכן מתאים</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.featureCard, { zIndex: 1000 }]} 
            onPress={() => {
              console.log('🔴 ProfileScreen logout button pressed');
              handleLogout();
            }}
            activeOpacity={0.7}
          >
            <View style={[styles.featureIcon, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.featureIconText}>🚪</Text>
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>התנתקות</Text>
              <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>יציאה מהחשבון</Text>
            </View>
          </TouchableOpacity>
        </View>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle(),
  content: {
    flex: 1,
    padding: 20,
  },
  scrollContent: createWebCompatibleScrollContentStyle(),
  userSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarText: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  userRole: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  section: {
    marginBottom: 20,
  },
  featureCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 2,
  },
  featureIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  featureIconText: {
    fontSize: 24,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  featureSubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
  },
});
