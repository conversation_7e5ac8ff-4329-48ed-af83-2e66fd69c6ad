import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { getTextAlign } from '../../utils/rtl';
import { ChatStackParamList } from '../../navigation/types';

type CreateGroupScreenNavigationProp = StackNavigationProp<ChatStackParamList, 'CreateGroup'>;

export default function CreateGroupScreen() {
  const navigation = useNavigation<CreateGroupScreenNavigationProp>();
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupType, setGroupType] = useState<'private' | 'public'>('private');

  const handleNext = () => {
    if (!groupName.trim()) {
      Alert.alert('שגיאה', 'אנא הזן שם לקבוצה');
      return;
    }

    const groupData = {
      name: groupName.trim(),
      description: groupDescription.trim(),
      type: groupType,
    };

    navigation.navigate('AddMembers', { groupData });
  };

  const nextButton = (
    <TouchableOpacity
      style={[styles.nextButton, !groupName.trim() && styles.nextButtonDisabled]}
      onPress={handleNext}
      disabled={!groupName.trim()}
    >
      <Text style={styles.nextButtonText}>הבא</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Custom Header with Step Info */}
      <View style={styles.customHeader}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>קבוצה חדשה</Text>
          <Text style={styles.headerSubtitle}>שלב 1 מתוך 2</Text>
        </View>
        {nextButton}
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.form}>
          {/* Photo Upload */}
          <View style={styles.photoSection}>
            <TouchableOpacity style={styles.photoUpload}>
              <Text style={styles.photoUploadIcon}>📷</Text>
            </TouchableOpacity>
            <Text style={styles.photoUploadLabel}>הוסף תמונת קבוצה</Text>
          </View>

          {/* Group Name */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>
              שם הקבוצה *
            </Text>
            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder="הזן שם לקבוצה..."
              value={groupName}
              onChangeText={setGroupName}
              maxLength={50}
              placeholderTextColor="#999"
            />
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>
              תיאור
            </Text>
            <TextInput
              style={[styles.input, styles.textArea, { textAlign: getTextAlign() }]}
              placeholder="על מה הקבוצה הזו?"
              value={groupDescription}
              onChangeText={setGroupDescription}
              maxLength={200}
              multiline
              numberOfLines={4}
              placeholderTextColor="#999"
            />
          </View>

          {/* Group Type */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>
              סוג קבוצה
            </Text>
            <View style={styles.groupTypeSelector}>
              <TouchableOpacity
                style={[
                  styles.groupTypeOption,
                  groupType === 'private' && styles.groupTypeOptionSelected
                ]}
                onPress={() => setGroupType('private')}
              >
                <Text style={styles.groupTypeIcon}>👥</Text>
                <Text style={[
                  styles.groupTypeLabel,
                  groupType === 'private' && styles.groupTypeLabelSelected
                ]}>
                  פרטית
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.groupTypeOption,
                  groupType === 'public' && styles.groupTypeOptionSelected
                ]}
                onPress={() => setGroupType('public')}
              >
                <Text style={styles.groupTypeIcon}>🌐</Text>
                <Text style={[
                  styles.groupTypeLabel,
                  groupType === 'public' && styles.groupTypeLabelSelected
                ]}>
                  ציבורית
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Tip Box */}
          <View style={styles.tipBox}>
            <Text style={styles.tipTitle}>💡 טיפ</Text>
            <Text style={[styles.tipContent, { textAlign: getTextAlign() }]}>
              קבוצות פרטיות נראות רק לחברים שאתה מזמין. קבוצות ציבוריות יכולות להתגלות על ידי חברי קהילה אחרים.
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  // Custom Header
  customHeader: {
    backgroundColor: '#667eea',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: Platform.OS === 'android' ? 35 : 15,
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  headerSubtitle: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  form: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // Photo Upload Section
  photoSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  photoUpload: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 3,
    borderColor: 'rgba(102, 126, 234, 0.3)',
    borderStyle: 'dashed',
  },
  photoUploadIcon: {
    fontSize: 30,
    color: 'white',
  },
  photoUploadLabel: {
    color: '#667eea',
    fontSize: 14,
    textAlign: 'center',
  },
  // Form Groups
  formGroup: {
    marginBottom: 25,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
  },
  input: {
    width: '100%',
    padding: 15,
    borderWidth: 2,
    borderColor: '#e1e8ed',
    borderRadius: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  // Group Type Selector
  groupTypeSelector: {
    flexDirection: 'row',
    gap: 10,
  },
  groupTypeOption: {
    flex: 1,
    padding: 15,
    borderWidth: 2,
    borderColor: '#e1e8ed',
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  groupTypeOptionSelected: {
    borderColor: '#667eea',
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  groupTypeIcon: {
    fontSize: 20,
    marginBottom: 5,
  },
  groupTypeLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#95a5a6',
  },
  groupTypeLabelSelected: {
    color: '#667eea',
  },
  // Tip Box
  tipBox: {
    backgroundColor: '#f8f9ff',
    padding: 15,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#667eea',
  },
  tipTitle: {
    fontSize: 12,
    color: '#667eea',
    fontWeight: '600',
    marginBottom: 5,
  },
  tipContent: {
    fontSize: 12,
    color: '#2c3e50',
    lineHeight: 16,
  },
  // Header Button
  nextButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 15,
  },
  nextButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  nextButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  // Bottom Button Container
  buttonContainer: {
    padding: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e1e8ed',
  },
  createButton: {
    backgroundColor: '#667eea',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
