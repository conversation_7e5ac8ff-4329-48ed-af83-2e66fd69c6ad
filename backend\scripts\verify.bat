@echo off
setlocal enabledelayedexpansion

echo Starting verification...

REM Check if required environment variables are set
if not defined VOLUMES_ROOT (
    echo Error: VOLUMES_ROOT is not set in .env file
    exit /b 1
)

REM Check if services are running
echo Checking services...
docker ps | findstr "kibbutz-postgres" >nul
if errorlevel 1 (
    echo Error: PostgreSQL service is not running
    exit /b 1
)
docker ps | findstr "kibbutz-auth" >nul
if errorlevel 1 (
    echo Error: Auth service is not running
    exit /b 1
)
docker ps | findstr "kibbutz-elasticsearch" >nul
if errorlevel 1 (
    echo Error: Elasticsearch service is not running
    exit /b 1
)
docker ps | findstr "kibbutz-kibana" >nul
if errorlevel 1 (
    echo Error: Kibana service is not running
    exit /b 1
)
docker ps | findstr "kibbutz-filebeat" >nul
if errorlevel 1 (
    echo Error: Filebeat service is not running
    exit /b 1
)
echo All required services are running.

REM Check if database exists
echo Checking database...
docker exec kibbutz-postgres psql -U postgres -c "SELECT 1 FROM pg_database WHERE datname='kibbutz_db'" | findstr /C:"1 row" >nul
if errorlevel 1 (
    echo Error: Database 'kibbutz_db' does not exist
    exit /b 1
)
echo Database exists.

REM Check if auth schema exists
echo Checking auth schema...
docker exec kibbutz-postgres psql -U postgres -d kibbutz_db -c "SELECT 1 FROM information_schema.schemata WHERE schema_name='auth'" | findstr /C:"1 row" >nul
if errorlevel 1 (
    echo Error: Auth schema does not exist
    exit /b 1
)
echo Auth schema exists.

REM Check if auth tables exist
echo Checking auth tables...
docker exec kibbutz-postgres psql -U postgres -d kibbutz_db -c "SELECT 1 FROM information_schema.tables WHERE table_schema='auth' AND table_name='users'" | findstr /C:"1 row" >nul
if errorlevel 1 (
    echo Error: Auth tables have not been created
    exit /b 1
)
echo Auth tables exist.

REM Check if Elasticsearch directories and configuration exist
echo Checking Elasticsearch setup...
if not exist "%ELASTIC_DATA_PATH%" (
    echo Error: Elasticsearch data directory does not exist
    exit /b 1
)
if not exist "%FILEBEAT_CONFIG_PATH%" (
    echo Error: Filebeat configuration file does not exist
    exit /b 1
)
if not exist "%FILEBEAT_LOG_PATH%" (
    echo Error: Filebeat logs directory does not exist
    exit /b 1
)
echo Elasticsearch directories and configuration exist.

REM Check if Elasticsearch is responding
echo Checking Elasticsearch health...
curl -s http://localhost:9200/_cluster/health | findstr "status" >nul
if errorlevel 1 (
    echo Error: Elasticsearch is not responding
    exit /b 1
)
echo Elasticsearch is healthy.

echo Verification completed successfully!
exit /b 0 