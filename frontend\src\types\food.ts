import { IconSet } from '@components';

export interface FoodItem {
  id: string;
  name: string;
  description: string;
  price: string;
  sellerName: string;
  sellerId: string;
  contactInfo: string;
  pickupDetails: string | null;
  inlinePhotoId: string | null;
  dataAiHint: string | null;
  createdAt: string | null;
  communityId: string;
  icon?: string;
  iconSet?: IconSet;
}

export interface CreateFoodItemData {
  name: string;
  description: string;
  price: string;
  pickupDetails?: string;
  contactInfo: string;
  icon?: string;
  iconSet?: IconSet;
  sellerName?: string;
} 