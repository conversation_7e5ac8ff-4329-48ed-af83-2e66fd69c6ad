import prisma from '../../config/prismaClient';
import { getServiceRoleClient } from '../../config/supabase';


import bcrypt from 'bcrypt';
//import { getModuleLogger } from '../../utils/logger';
import { UserRole, UserType } from '../../generated/prisma';
import { getModuleLogger } from '../../utils/logger';
//import { getSupabaseClient } from '../../config/supabase';
import { CreateUserDTO, CreateUserProfileDTO, UpdateUserDTO, UserResponse, UIPreferences } from './user.types';


const logger = getModuleLogger('User');

export class UserService {
  private async isFirstUser(): Promise<boolean> {
    const userCount = await prisma.user.count();
    return userCount === 0;
  }

  async createUser(data: CreateUserDTO): Promise<UserResponse> {
    try {
      const hashedPassword = await bcrypt.hash(data.password, 10);
      const isFirst = await this.isFirstUser();

      const user = await prisma.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          first_name: data.firstName || null,
          last_name: data.lastName || null,
          role: isFirst ? UserRole.ADMIN : data.role,
          user_type: data.userType,
          phone: data.phone || null,
          language: data.language || 'en',
          community_id: 'default',
          is_active: true,
          ui_preferences: data.uiPreferences as any || {
            theme: 'light',
            colorScheme: 'default',
            fontSize: 'medium',
            spacing: 'medium',
            highContrast: false
          },
        },
      });

      if (isFirst) {
        logger.info('First user created with ADMIN role', { userId: user.id, email: user.email });
      }

      return user;
    } catch (error) {
      logger.error(`Error creating user: ${error}`);
      throw error;
    }
  }

  async createUserProfile(data: CreateUserProfileDTO): Promise<UserResponse> {
    try {
      logger.debug('Creating user profile in service', {
        userId: data.id,
        email: data.email
      });

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { id: data.id }
      });

      if (existingUser) {
        logger.debug('User already exists', {
          userId: existingUser.id,
          email: existingUser.email
        });
        return existingUser;
      }

      const isFirst = await this.isFirstUser();

      logger.debug('Creating new user in database', {
        userId: data.id,
        email: data.email,
        role: isFirst ? UserRole.ADMIN : data.role,
        userType: data.userType
      });

      // Create new user
      const user = await prisma.user.create({
        data: {
          id: data.id,
          email: data.email,
          first_name: data.firstName || null,
          last_name: data.lastName || null,
          role: isFirst ? UserRole.ADMIN : data.role,
          user_type: data.userType,
          phone: data.phone || null,
          language: data.language || 'en',
          community_id: 'default',
          ui_preferences: {
            theme: 'light',
            notifications: true
          }
        }
      });

      if (isFirst) {
        logger.info('First user created with ADMIN role', { userId: user.id, email: user.email });
      }

      logger.debug('User created successfully', {
        userId: user.id,
        email: user.email
      });

      return user;
    } catch (error) {
      logger.error('Error creating user profile in service', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        data: {
          userId: data.id,
          email: data.email
        }
      });
      throw error;
    }
  }

  async updateUser(id: string, data: UpdateUserDTO): Promise<UserResponse> {
    try {
      const updateData: any = {};

      if (data.firstName !== undefined) updateData.first_name = data.firstName;
      if (data.lastName !== undefined) updateData.last_name = data.lastName;
      if (data.role !== undefined) updateData.role = data.role;
      if (data.userType !== undefined) updateData.user_type = data.userType;
      if (data.phone !== undefined) updateData.phone = data.phone;
      if (data.language !== undefined) updateData.language = data.language;
      if (data.isActive !== undefined) updateData.is_active = data.isActive;
      if (data.uiPreferences !== undefined) updateData.ui_preferences = data.uiPreferences as any;

      const user = await prisma.user.update({
        where: { id },
        data: updateData,
      });

      return user;
    } catch (error) {
      logger.error(`Error updating user: ${error}`);
      throw error;
    }
  }

  async getUserById(id: string): Promise<UserResponse | null> {
    try {
      logger.debug(`Service: Fetching user from database with ID: ${id}`);
      const user = await prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        logger.debug(`Service: No user found in database for ID: ${id}`);
        return null;
      }

      logger.debug(`Service: User found in database:`, {
        id: user.id,
        email: user.email,
        role: user.role,
        user_type: user.user_type,
        first_name: user.first_name,
        last_name: user.last_name
      });

      return user;
    } catch (error) {
      logger.error(`Error fetching user: ${error}`);
      throw error;
    }
  }

  async getAllUsers(): Promise<UserResponse[]> {
    try {
      const usersData = await prisma.user.findMany();
      return usersData;
    } catch (error) {
      logger.error(`Error fetching all users: ${error}`);
      throw error;
    }
  }

  async getUserCount(): Promise<number> {
    try {
      const count = await prisma.user.count();
      return count;
    } catch (error) {
      logger.error(`Error getting user count: ${error}`);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      // 1. Delete the user from the backend (Prisma)
      await prisma.user.delete({
        where: { id },
      });

      // 2. Delete the user from Supabase Auth using the service role key
      const supabaseAdmin = getServiceRoleClient();
      const { error } = await supabaseAdmin.auth.admin.deleteUser(id);

      if (error) {
        throw error;
      }
    } catch (error) {
      logger.error(`Error deleting user: ${error}`);
      throw error;
    }
  }

  async updateLastLogin(id: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id },
        data: {
          last_login: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating last login: ${error}`);
      throw error;
    }
  }

  async updateUIPreferences(id: string, uiPreferences: UIPreferences): Promise<UserResponse> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data: {
          ui_preferences: uiPreferences as any,
        },
      });

      return user;
    } catch (error) {
      logger.error(`Error updating UI preferences: ${error}`);
      throw error;
    }
  }

  async getUserUIPreferences(id: string): Promise<UIPreferences | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: { ui_preferences: true },
      });

      if (!user || !user.ui_preferences) return null;

      return user.ui_preferences as unknown as UIPreferences;
    } catch (error) {
      logger.error(`Error fetching UI preferences: ${error}`);
      throw error;
    }
  }

  async getUsersByTypeOrRole(): Promise<{ residents: UserResponse[], superAdmins: UserResponse[] }> {
    try {
      const residents = await prisma.user.findMany({
        where: {
          user_type: {
            in: [UserType.ADULT, UserType.YOUTH, UserType.CHILD]
          }
        },
      });

      const superAdmins = await prisma.user.findMany({
        where: {
          role: UserRole.ADMIN
        },
      });

      return {
        residents,
        superAdmins
      };
    } catch (error) {
      logger.error(`Error fetching users by type or role: ${error}`);
      throw error;
    }
  }

  async getPeople(): Promise<{ first_name: string | null; last_name: string | null; email: string; phone: string | null; user_type: string }[]> {
    try {
      const people = await prisma.user.findMany({
        select: {
          first_name: true,
          last_name: true,
          email: true,
          phone: true,
          user_type: true,
        }
      });
      return people;
    } catch (error) {
      logger.error(`Error fetching people: ${error}`);
      throw error;
    }
  }
}