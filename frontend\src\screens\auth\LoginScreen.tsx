import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@contexts';
import { getTextAlign, createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { WebCompatibleScrollView } from '@components';

export default function LoginScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    console.log('🚀 Login button clicked');

    if (!email || !password) {
      console.log('❌ Validation failed: Missing email or password');
      Alert.alert(t('common.error'), t('auth.fillAllFields'));
      return;
    }

    console.log('✅ Validation passed, starting login...');
    setLoading(true);

    try {
      console.log('🔐 Attempting login for:', email);

      await login({
        email: email.trim(),
        password: password,
      });

      console.log('🎉 Login successful!');
      // Navigation will be handled automatically by AuthContext
    } catch (error: any) {
      console.error('💥 Login failed:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      // Provide more specific error messages
      let errorMessage = error.message || t('auth.loginError');

      if (error.code === 'auth/user-not-found') {
        errorMessage = 'משתמש לא נמצא - בדוק את כתובת האימייל';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'סיסמה שגויה';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'כתובת אימייל לא תקינה';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'יותר מדי ניסיונות התחברות - נסה שוב מאוחר יותר';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'בעיית רשת - בדוק את החיבור לאינטרנט';
      }

      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <WebCompatibleScrollView
        contentContainerStyle={styles.scrollContent}
        headerHeight={0}
      >
        <View style={styles.content}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          {t('auth.login')}
        </Text>
        <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
          ברוכים הבאים לאפליקציית הקיבוץ
        </Text>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder={t('auth.email')}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder={t('auth.password')}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />

          <TouchableOpacity
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            <Text style={styles.buttonText}>
              {loading ? 'מתחבר...' : t('auth.loginButton')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.linkText}>שכחת סיסמה?</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigation.navigate('Register')}
          >
            <Text style={styles.linkText}>אין לך חשבון? הירשם כאן</Text>
          </TouchableOpacity>
        </View>
        </View>
      </WebCompatibleScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    ...createWebCompatibleContainerStyle(0),
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    ...createWebCompatibleScrollContentStyle(20),
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: 20,
  },
  content: {
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    color: '#666',
  },
  form: {
    width: '100%',
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 15,
  },
  linkText: {
    color: '#007AFF',
    fontSize: 14,
  },
});
