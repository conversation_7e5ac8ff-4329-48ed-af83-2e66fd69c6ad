# Design System Documentation

## Overview

This design system provides a consistent set of design tokens, utilities, and components for the Kibuttz app. It ensures visual consistency and maintainable code across the entire application.

## Design Tokens

### Spacing

Based on a 4px grid system for consistent spacing:

```typescript
import { getSpacing } from '@theme';

// Usage
const styles = {
  margin: getSpacing('md'), // 16px
  padding: getSpacing('lg'), // 24px
};
```

Available spacing tokens:
- `xs`: 4px
- `sm`: 8px  
- `md`: 16px
- `lg`: 24px
- `xl`: 32px
- `xxl`: 48px
- `xxxl`: 64px

### Colors

Semantic color system with multiple shades:

```typescript
import { getColor } from '@theme';

// Usage
const styles = {
  backgroundColor: getColor('primary'), // Default shade (500)
  color: getColor('primary', 600), // Specific shade
};
```

Available color families:
- `primary`: Main brand color (#667eea)
- `secondary`: Secondary brand color (#764ba2)
- `success`: Success states (#4ecdc4)
- `error`: Error states (#ff6b6b)
- `warning`: Warning states (#f9ca24)
- `info`: Info states (#45b7d1)
- `neutral`: Text and background colors

### Typography

Consistent typography scale:

```typescript
import { getTypography } from '@theme';

// Usage
const styles = {
  ...getTypography('lg', 'bold', 'lg'),
};
```

Available sizes: `xs`, `sm`, `base`, `lg`, `xl`, `2xl`, `3xl`, `4xl`, `5xl`, `6xl`
Available weights: `light`, `normal`, `medium`, `semibold`, `bold`, `extrabold`

### Border Radius

Consistent border radius scale:

```typescript
import { getBorderRadius } from '@theme';

// Usage
const styles = {
  borderRadius: getBorderRadius('md'), // 8px
};
```

Available values: `none`, `sm`, `base`, `md`, `lg`, `xl`, `2xl`, `3xl`, `full`

### Shadows

Predefined shadow styles:

```typescript
import { getShadow } from '@theme';

// Usage
const styles = {
  ...getShadow('md'),
};
```

Available shadows: `sm`, `base`, `md`, `lg`, `xl`

## Utility Functions

### Color Utilities

```typescript
import { getColorWithOpacity } from '@theme';

// Create semi-transparent colors
const semiTransparent = getColorWithOpacity('primary', 500, 0.5);
```

### Button Styles

```typescript
import { createButtonStyle } from '@theme';

// Create consistent button styles
const buttonStyle = createButtonStyle('primary', 'md');
```

### Card Styles

```typescript
import { createCardStyle } from '@theme';

// Create consistent card styles
const cardStyle = createCardStyle(true); // elevated
```

## Styled Components

### StyledButton

```typescript
import { StyledButton } from '@components';

<StyledButton
  title="Click me"
  onPress={handlePress}
  variant="primary"
  size="md"
/>
```

Props:
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost'
- `size`: 'sm' | 'md' | 'lg'
- `disabled`: boolean
- `loading`: boolean

### StyledCard

```typescript
import { StyledCard } from '@components';

<StyledCard elevated padding="lg">
  <Text>Card content</Text>
</StyledCard>
```

Props:
- `elevated`: boolean (adds shadow)
- `padding`: 'none' | 'sm' | 'md' | 'lg'

### Typography

```typescript
import { Typography } from '@components';

<Typography variant="h1" color="primary">
  Heading Text
</Typography>
```

Props:
- `variant`: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline'
- `color`: 'primary' | 'secondary' | 'text' | 'textSecondary' | 'error' | 'success' | 'warning' | 'info'
- `align`: 'left' | 'center' | 'right' | 'justify'

## Migration Guide

### Replacing Hardcoded Values

Before:
```typescript
const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#667eea',
    borderRadius: 8,
  },
});
```

After:
```typescript
import { getSpacing, getColor, getBorderRadius } from '@theme';

const styles = StyleSheet.create({
  container: {
    padding: getSpacing('md'),
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('md'),
  },
});
```

### Using Styled Components

Before:
```typescript
<TouchableOpacity style={customButtonStyle}>
  <Text style={customTextStyle}>Button</Text>
</TouchableOpacity>
```

After:
```typescript
<StyledButton
  title="Button"
  onPress={handlePress}
  variant="primary"
/>
```

## Best Practices

1. **Always use design tokens** instead of hardcoded values
2. **Use styled components** for common UI patterns
3. **Maintain consistency** by using the same tokens across similar elements
4. **Test on multiple screen sizes** to ensure responsive design
5. **Follow the spacing grid** for consistent layouts
6. **Use semantic colors** (success, error, warning) for appropriate contexts

## Adding New Tokens

When adding new design tokens:

1. Add to `designTokens.ts`
2. Update utility functions in `utils.ts`
3. Add TypeScript types
4. Update this documentation
5. Create examples in Storybook (if available)

## Performance Considerations

- Design tokens are constants and don't cause re-renders
- Styled components use memoization where appropriate
- Shadow styles are optimized for React Native performance
- Color utilities cache computed values
