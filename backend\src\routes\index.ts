import { Router } from 'express';
import { getModuleLogger } from '../utils/logger';
import { authenticateToken } from '../middleware/auth';
import authRouter from '../modules/auth/auth.router';
import userRouter from '../modules/user/user.router';
import eventRouter from '../modules/event/event.router';
import foodRouter from '../modules/food/food.router';
import jobRouter from '../modules/job/job.router';
import surveyRouter from '../modules/survey/survey.router';
import dealRouter from '../modules/deal/deal.router';
import chatRouter from '../modules/chat/chat.router';
import debugRouter from '../modules/debug/debug.router';

const logger = getModuleLogger('Router');

// Add debug logging to verify log level
logger.debug('Router module initialized with level:', { level: logger.getLevel() });

const router = Router();

// Log all requests to the router
router.use((req, _res, next) => {
  logger.debug('Router received request:', {
    method: req.method,
    url: req.url,
    path: req.path,
    params: req.query
  });
  next();
});

// Public routes (no authentication required)
router.use('/auth', authRouter);
logger.info('Auth routes registered at /api/auth (public)');

// Protected routes (authentication required)
router.use('/users', authenticateToken, userRouter);
logger.info('User routes registered at /api/users (protected)');

router.use('/events', authenticateToken, eventRouter);
logger.info('Event routes registered at /api/events (protected)');

router.use('/food', authenticateToken, foodRouter);
logger.info('Food routes registered at /api/food (protected)');

router.use('/jobs', authenticateToken, jobRouter);
logger.info('Job routes registered at /api/jobs (protected)');

router.use('/surveys', authenticateToken, surveyRouter);
logger.info('Survey routes registered at /api/surveys (protected)');

router.use('/deals', authenticateToken, dealRouter);
logger.info('Deal routes registered at /api/deals (protected)');

router.use('/chat', authenticateToken, chatRouter);
logger.info('Chat routes registered at /api/chat (protected)');

router.use('/debug', authenticateToken, debugRouter);
logger.info('Debug routes registered at /api/debug (protected)');

export default router;
