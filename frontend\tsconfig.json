{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@components/*": ["src/components/*"], "@components": ["src/components"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@theme/*": ["src/theme/*"], "@utils/*": ["src/utils/*"], "@utils": ["src/utils"], "@hooks/*": ["src/hooks/*"], "@hooks": ["src/hooks"], "@services/*": ["src/services/*"], "@services": ["src/services"], "@contexts/*": ["src/contexts/*"], "@contexts": ["src/contexts"], "@i18n/*": ["src/i18n/*"], "@assets/*": ["src/assets/*"], "@config/*": ["src/config/*"], "@types/*": ["src/types/*"], "@lib/*": ["src/lib/*"], "@constants/*": ["src/constants/*"], "@constants": ["src/constants"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}