import { createClient } from '@supabase/supabase-js';
import { envRegistry } from '../utils/env';

// Register Supabase environment variables
envRegistry.registerMultiple([
  {
    name: 'SUPABASE_PUBLIC_URL',
    description: 'Supabase project URL',
    required: true,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_ANON_KEY',
    description: 'Supabase anonymous key',
    required: true,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_SERVICE_ROLE_KEY',
    description: 'Supabase service role key',
    required: false,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_JWT_SECRET',
    description: 'Supabase JWT secret',
    required: false,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_AUTH_EXPIRY',
    description: 'Supabase auth token expiry in seconds',
    defaultValue: '3600',
    required: false,
    module: 'Supabase'
  },
  {
    name: '<PERSON><PERSON><PERSON>ASE_AUTH_REFRESH_EXPIRY',
    description: 'Supabase auth refresh token expiry in seconds',
    defaultValue: '604800',
    required: false,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_AUTH_COOKIE_NAME',
    description: 'Supabase auth cookie name',
    defaultValue: 'sb-auth-token',
    required: false,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_AUTH_COOKIE_DOMAIN',
    description: 'Supabase auth cookie domain',
    required: false,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_AUTH_COOKIE_SECURE',
    description: 'Whether to use secure cookies',
    defaultValue: 'true',
    required: false,
    module: 'Supabase'
  },
  {
    name: 'SUPABASE_AUTH_COOKIE_SAME_SITE',
    description: 'Supabase auth cookie same-site policy',
    defaultValue: 'lax',
    required: false,
    module: 'Supabase'
  }
]);

let supabaseClient: ReturnType<typeof createClient> | null = null;

export const getSupabaseClient = () => {
  if (!supabaseClient) {
    const supabaseUrl = process.env['SUPABASE_PUBLIC_URL'];
    const supabaseAnonKey = process.env['SUPABASE_ANON_KEY'];

    if (!supabaseUrl) {
      throw new Error('SUPABASE_PUBLIC_URL is required');
    }
    if (!supabaseAnonKey) {
      throw new Error('SUPABASE_ANON_KEY is required');
    }

    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      },
    });
  }
  return supabaseClient;
};

export const getServiceRoleClient = () => {
  const supabaseUrl = process.env['SUPABASE_PUBLIC_URL'];
  const serviceRoleKey = process.env['SUPABASE_SERVICE_ROLE_KEY'];

  if (!supabaseUrl) {
    throw new Error('SUPABASE_PUBLIC_URL is required');
  }
  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for service role operations');
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Helper function to get user session
export const getUserSession = async (accessToken: string) => {
  try {
    const client = getSupabaseClient();
    const { data: { user }, error } = await client.auth.getUser(accessToken);
    if (error) throw error;
    return user;
  } catch (error) {
    console.error('Error getting user session:', error);
    return null;
  }
};

// Helper function to verify JWT token
export const verifyToken = async (token: string) => {
  try {
    const client = getSupabaseClient();
    const { data: { user }, error } = await client.auth.getUser(token);
    if (error) throw error;
    return user;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}; 