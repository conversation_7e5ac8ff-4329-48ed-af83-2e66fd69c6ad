-- CreateEnum
CREATE TYPE "MessageType" AS ENUM ('TEXT', 'IMAGE');

-- C<PERSON><PERSON>num
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'MODERATOR', 'USER');

-- CreateEnum
CREATE TYPE "UserType" AS ENUM ('ADULT', 'YOUTH', 'CHILD', 'EXTERNAL');

-- Create<PERSON>num
CREATE TYPE "EventType" AS ENUM ('COMMUNITY', 'TENANT');

-- CreateEnum
CREATE TYPE "JobStatus" AS ENUM ('OPEN', 'IN_PROGRESS', 'COMPLETED');

-- CreateEnum
CREATE TYPE "JobType" AS ENUM ('OFFER', 'REQUEST');

-- CreateEnum
CREATE TYPE "SurveyStatus" AS ENUM ('OPEN', 'CLOSED');

-- CreateEnum
CREATE TYPE "DealStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'CANCELLED');

-- AlterTable
ALTER TABLE "FoodItem" ADD COLUMN "image_url" TEXT; 