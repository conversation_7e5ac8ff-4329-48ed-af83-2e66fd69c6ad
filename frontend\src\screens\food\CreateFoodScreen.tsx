import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon, IconPicker } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { FoodStackParamList } from '@navigation/types';
import { StackNavigationProp } from '@react-navigation/stack';
import { IconSet } from '@components';
import { CreateFoodItemData } from '../../types/food';
import { apiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

type CreateFoodScreenNavigationProp = StackNavigationProp<FoodStackParamList, 'CreateFood'>;

export default function CreateFoodScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateFoodScreenNavigationProp>();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [pickupDetails, setPickupDetails] = useState('');
  const [contactInfo, setContactInfo] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('🍲');
  const [selectedIconSet, setSelectedIconSet] = useState<IconSet>('MaterialCommunityIcons');
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  const handleSubmit = async () => {
    if (!name || !description || !price || !contactInfo) {
      Alert.alert('שגיאה', 'אנא מלא את כל השדות החובה');
      return;
    }

    setLoading(true);
    try {
      const sellerName = user?.first_name && user?.last_name
        ? `${user.first_name} ${user.last_name}`
        : user?.first_name || user?.email || 'אנונימי';
      const foodData: CreateFoodItemData = {
        name,
        description,
        price,
        pickupDetails,
        contactInfo,
        icon: selectedIcon,
        iconSet: selectedIconSet,
        sellerName,
      };

      const response = await apiService.createFoodItem(foodData);
      if (response.data) {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error creating food item:', error);
      Alert.alert('שגיאה', 'אירעה שגיאה ביצירת המוצר');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="🍲 פרסם מוצר חדש"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.form}>
          <IconPicker
            selectedIcon={selectedIcon}
            onIconSelect={setSelectedIcon}
            label="בחר אייקון למוצר"
            defaultTab="food"
          />

          <View style={styles.inputGroup}>
            <Text style={styles.label}>שם המוצר *</Text>
            <TextInput
              style={[styles.input, { textAlign: 'right' }]}
              value={name}
              onChangeText={setName}
              placeholder="לדוגמה: מרק ירקות ביתי"
              placeholderTextColor={getColor('neutral', 400)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>תיאור *</Text>
            <TextInput
              style={[styles.input, styles.textArea, { textAlign: 'right' }]}
              value={description}
              onChangeText={setDescription}
              placeholder="תאר את המוצר שלך..."
              placeholderTextColor={getColor('neutral', 400)}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>מחיר (₪) *</Text>
            <TextInput
              style={[styles.input, { textAlign: 'right' }]}
              value={price}
              onChangeText={setPrice}
              placeholder="לדוגמה: 15"
              placeholderTextColor={getColor('neutral', 400)}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>פרטי איסוף</Text>
            <TextInput
              style={[styles.input, { textAlign: 'right' }]}
              value={pickupDetails}
              onChangeText={setPickupDetails}
              placeholder="לדוגמה: איסוף אחרי 17:00 בבית 12"
              placeholderTextColor={getColor('neutral', 400)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>פרטי התקשרות *</Text>
            <TextInput
              style={[styles.input, { textAlign: 'right' }]}
              value={contactInfo}
              onChangeText={setContactInfo}
              placeholder="לדוגמה: טלפון או וואטסאפ"
              placeholderTextColor={getColor('neutral', 400)}
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'שולח...' : 'פרסם מוצר'}
            </Text>
          </TouchableOpacity>
        </View>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  form: {
    gap: getSpacing('lg'),
  },
  inputGroup: {
    gap: getSpacing('xs'),
  },
  label: {
    ...getTypography('sm', 'bold'),
    color: getColor('neutral', 700),
  },
  input: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('md'),
    ...getTypography('base'),
    color: getColor('neutral', 900),
    borderWidth: 1,
    borderColor: getColor('neutral', 200),
  },
  textArea: {
    height: 100,
  },
  submitButton: {
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('full'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    marginTop: getSpacing('xl'),
    ...getShadow('sm'),
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: getColor('white'),
    ...getTypography('base', 'bold'),
  },
}); 