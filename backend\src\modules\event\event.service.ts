import prisma from '../../config/prismaClient';
import { EventResponse } from './event.types';
import { minioStorageClient, BUCKETS, StorageFile } from '../../services/minio-storage.service';
import { logger } from '../../utils/logger';

export class EventService {
  constructor() {
    // Using centralized Prisma client
  }

  private validateImageFile(file: { buffer: Buffer; mimetype: string }): void {
    const bucketConfig = minioStorageClient.getBucketConfig(BUCKETS.EVENT_IMAGES);
    
    if (!bucketConfig.allowedMimeTypes?.includes(file.mimetype)) {
      throw new Error(`Invalid image type. Allowed types: ${bucketConfig.allowedMimeTypes?.join(', ')}`);
    }
    if (bucketConfig.fileSizeLimit && file.buffer.length > bucketConfig.fileSizeLimit) {
      throw new Error(`Image size exceeds maximum limit of ${bucketConfig.fileSizeLimit / (1024 * 1024)}MB`);
    }
  }

  private async uploadImage(file: Express.Multer.File | { buffer: Buffer; filename: string; mimetype: string }): Promise<string> {
    try {
      logger.info('[EVENT] Uploading image before creating event');
      // Convert the file object to match StorageFile if needed
      const storageFile: StorageFile = 'fieldname' in file ? file : {
        fieldname: 'image',
        originalname: file.filename,
        encoding: '7bit',
        mimetype: file.mimetype,
        buffer: file.buffer,
        size: file.buffer.length,
      };
      
      // Use the BUCKETS constant for consistency
      const bucketName = BUCKETS.EVENT_IMAGES;
      logger.info(`[EVENT] Using bucket: ${bucketName}`);
      
      return await minioStorageClient.uploadImage(storageFile, bucketName);
    } catch (error) {
      logger.error('[EVENT] Error uploading image:', error);
      throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async createEvent(event: any, imageFile?: Express.Multer.File): Promise<EventResponse> {
    let imageUrl = event.imageUrl || event.image_url;

    // Handle image upload if provided
    if (imageFile) {
      this.validateImageFile(imageFile);
      imageUrl = await this.uploadImage(imageFile);
    }

    const dbEvent = await prisma.event.create({
      data: {
        community_id: event.community_id,
        title: event.title,
        description: event.description,
        date: event.date,
        location: event.location,
        type: event.type.toUpperCase(), // Convert to uppercase for enum
        icon: event.icon || "📅", // Default to calendar emoji if not provided
        image_url: imageUrl,
        data_ai_hint: event.dataAiHint || event.data_ai_hint,
        attendees: event.attendees || 0,
        created_by: event.created_by
      }
    });

    // Transform to frontend-compatible format
    return this.transformEventForResponse(dbEvent);
  }

  private transformEventForResponse(dbEvent: any): EventResponse {
    // Format creator name from user data
    let createdByName = 'משתמש לא ידוע'; // Default fallback
    if (dbEvent.user) {
      const firstName = dbEvent.user.first_name || '';
      const lastName = dbEvent.user.last_name || '';
      if (firstName || lastName) {
        createdByName = `${firstName} ${lastName}`.trim();
      } else if (dbEvent.user.email) {
        // Fallback to email if no name available
        createdByName = dbEvent.user.email.split('@')[0];
      }
    }

    return {
      id: dbEvent.id,
      title: dbEvent.title,
      description: dbEvent.description,
      date: dbEvent.date,
      location: dbEvent.location,
      type: dbEvent.type.toLowerCase(), // Convert back to lowercase for frontend
      icon: dbEvent.icon,
      imageUrl: dbEvent.image_url,
      dataAiHint: dbEvent.data_ai_hint,
      createdAt: dbEvent.created_at,
      attendees: dbEvent.attendees,
      createdBy: createdByName, // Now contains the actual name
      created_by: dbEvent.created_by, // <-- Add this for test compatibility
      rsvp: [], // Empty array for now
      comments: [], // Empty array for now
      reactions: [] // Empty array for now
    } as any;
  }

  async getEvents(): Promise<EventResponse[]> {
    const dbEvents = await prisma.event.findMany({
      include: {
        user: {
          select: {
            first_name: true,
            last_name: true,
            email: true
          }
        }
      },
      orderBy: { date: 'asc' }
    });
    return dbEvents.map(event => this.transformEventForResponse(event));
  }

  async getEvent(id: string): Promise<any> {
    return prisma.event.findUnique({ where: { id } });
  }

  async updateEvent(id: string, data: any, imageFile?: Express.Multer.File): Promise<EventResponse | null> {
    let imageUrl = data.imageUrl || data.image_url;

    // Handle image upload if provided
    if (imageFile) {
      this.validateImageFile(imageFile);
      imageUrl = await this.uploadImage(imageFile);
    }

    const dbEvent = await prisma.event.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description,
        date: data.date,
        location: data.location,
        type: data.type?.toUpperCase(),
        icon: data.icon,
        image_url: imageUrl,
        data_ai_hint: data.dataAiHint || data.data_ai_hint,
        attendees: data.attendees
      },
      include: {
        user: {
          select: {
            first_name: true,
            last_name: true,
            email: true
          }
        }
      }
    });
    return this.transformEventForResponse(dbEvent);
  }

  async deleteEvent(id: string): Promise<void> {
    await prisma.event.delete({
      where: { id }
    });
  }

  async getEventById(id: string): Promise<any> {
    return prisma.event.findUnique({ where: { id } });
  }

  async isOwner(eventId: string, userId: string): Promise<boolean> {
    const event = await prisma.event.findUnique({ where: { id: eventId } });
    return event?.created_by === userId;
  }
} 