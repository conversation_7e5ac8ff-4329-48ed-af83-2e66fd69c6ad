import { UIPreferences } from '../types';
import { apiService } from './api';

class UserPreferencesService {
  async getUserPreferences(userId: string): Promise<UIPreferences | null> {
    try {
      const response = await apiService.getUserProfile(userId);
      
      if (response.error) {
        if (response.error === 'Not found') {
          return null; // No preferences found
        }
        throw new Error(response.error);
      }

      return response.data?.ui_preferences || null;
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      throw error;
    }
  }

  async updateUserPreferences(userId: string, preferences: UIPreferences): Promise<UIPreferences> {
    try {
      const response = await apiService.updateUserProfile(userId, { ui_preferences: preferences });

      if (response.error) {
        throw new Error(response.error);
      }

      return response.data?.ui_preferences || preferences;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }
}

export const userPreferencesService = new UserPreferencesService();
