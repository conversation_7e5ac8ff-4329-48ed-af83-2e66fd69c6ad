interface MultipartResponse<T> {
  data: T;
  imageData: string | null;
}

/**
 * Parses a multipart response containing JSON data and an image
 * @param response The fetch Response object
 * @returns An object containing the parsed data and image data
 */
export async function parseMultipartResponse<T>(response: Response): Promise<MultipartResponse<T>> {
  const contentType = response.headers.get('content-type');
  
  if (!contentType?.includes('multipart/mixed')) {
    // Regular JSON response
    const data = await response.json();
    return { data, imageData: null };
  }

  // Get the response as blob
  const blob = await response.blob();
  
  // Create a FileReader to read the blob
  const reader = new FileReader();
  
  return new Promise((resolve, reject) => {
    reader.onload = () => {
      try {
        const arrayBuffer = reader.result as ArrayBuffer;
        const uint8Array = new Uint8Array(arrayBuffer);

        // Get the boundary from the content type
        const boundaryMatch = contentType.match(/boundary=([^;]+)/);
        if (!boundaryMatch) {
          throw new Error('Invalid multipart response: no boundary found');
        }
        const boundary = boundaryMatch[1];
        const boundaryBytes = new TextEncoder().encode(`--${boundary}`);
        const doublecrlfBytes = new TextEncoder().encode('\r\n\r\n');

        // Find boundary positions in the binary data
        const boundaryPositions: number[] = [];
        for (let i = 0; i <= uint8Array.length - boundaryBytes.length; i++) {
          let match = true;
          for (let j = 0; j < boundaryBytes.length; j++) {
            if (uint8Array[i + j] !== boundaryBytes[j]) {
              match = false;
              break;
            }
          }
          if (match) {
            boundaryPositions.push(i);
          }
        }

        console.log('[MultipartResponse] Found boundaries at positions:', boundaryPositions);

        if (boundaryPositions.length < 2) {
          throw new Error('Invalid multipart response: insufficient boundaries found');
        }

        // Extract JSON part (first part after initial boundary)
        const firstPartStart = boundaryPositions[0] + boundaryBytes.length;
        const firstPartEnd = boundaryPositions[1];
        const firstPartBytes = uint8Array.slice(firstPartStart, firstPartEnd);

        // Find the double CRLF that separates headers from content
        let jsonStart = -1;
        for (let i = 0; i <= firstPartBytes.length - doublecrlfBytes.length; i++) {
          let match = true;
          for (let j = 0; j < doublecrlfBytes.length; j++) {
            if (firstPartBytes[i + j] !== doublecrlfBytes[j]) {
              match = false;
              break;
            }
          }
          if (match) {
            jsonStart = i + doublecrlfBytes.length;
            break;
          }
        }

        if (jsonStart === -1) {
          throw new Error('Invalid multipart response: JSON content not found');
        }

        // Extract and parse JSON
        const jsonBytes = firstPartBytes.slice(jsonStart);
        // Remove trailing CRLF
        let jsonEndIndex = jsonBytes.length;
        while (jsonEndIndex > 0 && (jsonBytes[jsonEndIndex - 1] === 13 || jsonBytes[jsonEndIndex - 1] === 10)) {
          jsonEndIndex--;
        }
        const jsonText = new TextDecoder('utf-8').decode(jsonBytes.slice(0, jsonEndIndex));
        const data = JSON.parse(jsonText) as T;

        // Extract image part (second part)
        let imageData: Uint8Array | null = null;
        if (boundaryPositions.length >= 3) {
          const secondPartStart = boundaryPositions[1] + boundaryBytes.length;
          const secondPartEnd = boundaryPositions[2];
          const secondPartBytes = uint8Array.slice(secondPartStart, secondPartEnd);

          // Find the double CRLF that separates headers from image content
          let imageStart = -1;
          for (let i = 0; i <= secondPartBytes.length - doublecrlfBytes.length; i++) {
            let match = true;
            for (let j = 0; j < doublecrlfBytes.length; j++) {
              if (secondPartBytes[i + j] !== doublecrlfBytes[j]) {
                match = false;
                break;
              }
            }
            if (match) {
              imageStart = i + doublecrlfBytes.length;
              break;
            }
          }

          if (imageStart !== -1) {
            // Extract image bytes (remove trailing CRLF)
            let imageEndIndex = secondPartBytes.length;
            while (imageEndIndex > imageStart && (secondPartBytes[imageEndIndex - 1] === 13 || secondPartBytes[imageEndIndex - 1] === 10)) {
              imageEndIndex--;
            }
            imageData = secondPartBytes.slice(imageStart, imageEndIndex);
          }
        }

        if (imageData) {
          console.log('[MultipartResponse] Extracted image data:', {
            size: imageData.length,
            firstBytes: Array.from(imageData.slice(0, 10)).map(b => b.toString(16)).join(' ')
          });

          // Convert image data to base64
          const base64Image = arrayBufferToBase64(imageData);

          // Create a proper data URI
          const dataUri = `data:image/jpeg;base64,${base64Image}`;

          console.log('[MultipartResponse] Created data URI:', {
            length: dataUri.length,
            prefix: dataUri.substring(0, 50)
          });

          resolve({ data, imageData: dataUri });
        } else {
          console.log('[MultipartResponse] No image data found');
          resolve({ data, imageData: null });
        }
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read response data'));
    };
    
    // Read the blob as ArrayBuffer
    reader.readAsArrayBuffer(blob);
  });
}

/**
 * Converts an ArrayBuffer to a base64 string
 * @param buffer The ArrayBuffer to convert
 * @returns A base64 string
 */
function arrayBufferToBase64(buffer: ArrayBuffer | Uint8Array): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

/**
 * Cleans up a blob URL created by parseMultipartResponse
 * @param imageUrl The blob URL to revoke
 */
export function cleanupImageUrl(imageUrl: string | null): void {
  if (imageUrl) {
    URL.revokeObjectURL(imageUrl);
  }
} 