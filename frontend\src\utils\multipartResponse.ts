interface MultipartResponse<T> {
  data: T;
  imageData: string | null;
}

/**
 * Parses a multipart response containing JSON data and an image
 * @param response The fetch Response object
 * @returns An object containing the parsed data and image data
 */
export async function parseMultipartResponse<T>(response: Response): Promise<MultipartResponse<T>> {
  const contentType = response.headers.get('content-type');
  
  if (!contentType?.includes('multipart/mixed')) {
    // Regular JSON response
    const data = await response.json();
    return { data, imageData: null };
  }

  // Get the response as blob
  const blob = await response.blob();
  
  // Create a FileReader to read the blob
  const reader = new FileReader();
  
  return new Promise((resolve, reject) => {
    reader.onload = () => {
      try {
        const arrayBuffer = reader.result as ArrayBuffer;
        const uint8Array = new Uint8Array(arrayBuffer);
        
        // Convert the first part to text for JSON parsing
        const textDecoder = new TextDecoder('utf-8');
        const text = textDecoder.decode(uint8Array);
        
        // Get the boundary from the content type
        const boundaryMatch = contentType.match(/boundary=([^;]+)/);
        if (!boundaryMatch) {
          throw new Error('Invalid multipart response: no boundary found');
        }
        const boundary = boundaryMatch[1];
        
        // Split the response into parts
        const parts = text.split(`--${boundary}`);
        
        // First part is empty, second part contains JSON data
        const jsonPart = parts[1].trim();
        const jsonContent = jsonPart.split('\r\n\r\n')[1];
        const data = JSON.parse(jsonContent) as T;
        
        // Third part contains image data
        const imagePart = parts[2].trim();
        const imageContent = imagePart.split('\r\n\r\n')[1];
        
        // Get the image data from the array buffer
        const imageStartIndex = text.indexOf('\r\n\r\n', text.indexOf('Content-Type: image/jpeg')) + 4;
        const imageEndIndex = text.lastIndexOf(`--${boundary}--`);
        const imageData = uint8Array.slice(imageStartIndex, imageEndIndex);
        
        // Convert image data to base64
        const base64Image = btoa(String.fromCharCode.apply(null, Array.from(imageData)));
        
        resolve({ data, imageData: base64Image });
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read response data'));
    };
    
    // Read the blob as ArrayBuffer
    reader.readAsArrayBuffer(blob);
  });
}

/**
 * Cleans up a blob URL created by parseMultipartResponse
 * @param imageUrl The blob URL to revoke
 */
export function cleanupImageUrl(imageUrl: string | null): void {
  if (imageUrl) {
    URL.revokeObjectURL(imageUrl);
  }
} 