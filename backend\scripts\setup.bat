@echo off
setlocal enabledelayedexpansion

echo Starting setup...

REM Load environment variables from .env file in parent directory
for /f "tokens=*" %%a in (C:\Kibuttz\backend\.env) do (
    set %%a
)

REM Check if required environment variables are set
if not defined VOLUMES_ROOT (
    echo Error: VOLUMES_ROOT is not set in .env file
    exit /b 1
)

REM Create Elasticsearch and Filebeat directories
echo Creating Elasticsearch and Filebeat directories...
if not exist "%ELASTIC_DATA_PATH%" mkdir "%ELASTIC_DATA_PATH%"
if not exist "%FILEBEAT_CONFIG_PATH%\.." mkdir "%FILEBEAT_CONFIG_PATH%\.."
if not exist "%FILEBEAT_LOG_PATH%" mkdir "%FILEBEAT_LOG_PATH%"

REM Create Filebeat configuration file if it doesn't exist
if not exist "%FILEBEAT_CONFIG_PATH%" (
    echo Creating Filebeat configuration file...
    (
        echo filebeat.inputs:
        echo - type: container
        echo   paths:
        echo     - '/var/lib/docker/containers/*/*.log'
        echo   processors:
        echo     - add_docker_metadata:
        echo         host: "unix:///var/run/docker.sock"
        echo.
        echo setup.kibana:
        echo   host: "kibana:5601"
        echo.
        echo output.elasticsearch:
        echo   hosts: ["elasticsearch:9200"]
        echo   indices:
        echo     - index: "filebeat-%%{+yyyy.MM.dd}"
        echo.
        echo logging.level: info
        echo logging.to_files: true
        echo logging.files:
        echo   path: /var/log/filebeat
        echo   name: filebeat
        echo   keepfiles: 7
        echo   permissions: 0644
    ) > "%FILEBEAT_CONFIG_PATH%"
    echo Filebeat configuration file created successfully!
)
echo Directories created successfully!

REM Wait for PostgreSQL to be ready
echo Waiting for PostgreSQL to be ready...
:wait_loop
docker exec kibbutz-postgres pg_isready -U postgres >nul 2>&1
if errorlevel 1 (
    timeout /t 1 >nul
    goto wait_loop
)
echo PostgreSQL is ready!

REM Create database if it doesn't exist
echo Checking if database exists...
docker exec kibbutz-postgres psql -U postgres -c "SELECT 1 FROM pg_database WHERE datname='kibbutz_db'" | findstr /C:"1 row" >nul
if errorlevel 1 (
    echo Creating database...
    docker exec kibbutz-postgres psql -U postgres -c "CREATE DATABASE kibbutz_db;"
    echo Database created successfully!
) else (
    echo Database already exists.
)

REM Create auth schema
echo Creating auth schema...
docker exec kibbutz-postgres psql -U postgres -d kibbutz_db -c "CREATE SCHEMA IF NOT EXISTS auth;"
echo Auth schema created successfully!

echo Creating food-images bucket in Supabase storage...
curl -X POST "%SUPABASE_STORAGE_URL%/storage/v1/bucket" ^
     -H "apikey: %SUPABASE_SERVICE_ROLE_KEY%" ^
     -H "Content-Type: application/json" ^
     -d "{\"name\": \"food-images\", \"public\": true}"
echo food-images bucket created successfully!

echo Setup completed successfully! 