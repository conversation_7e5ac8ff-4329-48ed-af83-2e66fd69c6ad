import { Router } from 'express';
import { <PERSON>t<PERSON><PERSON>roller } from './chat.controller';
import { ChatService } from './chat.service';
import { authenticateToken } from '../../middleware/auth';
import { requireModeratorOrAdmin } from '../../middleware/rbacMiddleware';
import multer from 'multer';

const router = Router();
const chatService = new ChatService();
const chatController = new ChatController(chatService);
const upload = multer();

// All chat routes require authentication
router.use(authenticateToken);

// Routes that require moderator or admin access
router.post('/groups', requireModeratorOrAdmin(), chatController.createChatGroup.bind(chatController));
router.delete('/groups/:id', requireModeratorOrAdmin(), chatController.deleteChatGroup.bind(chatController));

// Regular user routes (ownership checks are handled in the controller)
router.get('/groups', chatController.getChatGroups.bind(chatController));
router.get('/groups/:id', chatController.getChatGroup.bind(chatController));
router.put('/groups/:id', chatController.updateChatGroup.bind(chatController));
router.post('/messages', chatController.createChatMessage.bind(chatController));
router.put('/messages/:id', chatController.updateChatMessage.bind(chatController));
router.delete('/messages/:id', chatController.deleteChatMessage.bind(chatController));

// Chat Message routes
router.get('/messages', chatController.getChatMessages.bind(chatController));
router.get('/groups/:groupId/messages', chatController.getChatMessagesByGroupId.bind(chatController));

router.post('/messages/image', upload.single('file'), chatController.createImageMessage.bind(chatController));

export default router; 