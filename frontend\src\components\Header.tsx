import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { createHeaderButtonStyle, getShadow, getSpacing, getTypography, getColor } from '../theme';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
}

export default function Header({
  title,
  showBackButton = false,
  rightComponent,
  onBackPress
}: HeaderProps) {
  const navigation = useNavigation();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.header, { backgroundColor: getColor('primary'), paddingHorizontal: getSpacing('lg'), paddingVertical: getSpacing('lg') }]}>
      <StatusBar barStyle="light-content" backgroundColor={getColor('primary')} />

      {/* Left side - Back button */}
      <View style={styles.leftContainer}>
        {showBackButton ? (
          <TouchableOpacity style={createHeaderButtonStyle()} onPress={handleBackPress}>
            <Text style={[styles.backButtonText, { color: getColor('white') }]}>←</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.spacer} />
        )}
      </View>

      {/* Center - Title */}
      <View style={styles.centerContainer}>
        <Text style={[styles.headerTitle, { color: getColor('white') }]}>
          {title}
        </Text>
      </View>

      {/* Right side - Action button */}
      <View style={styles.rightContainer}>
        {rightComponent || <View style={styles.spacer} />}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + getSpacing('lg') : getSpacing('lg'),
    ...getShadow('md'),
  },
  leftContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  centerContainer: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    ...getTypography('lg', 'bold'),
  },
  backButtonText: {
    ...getTypography('lg', 'bold'),
  },
  spacer: {
    width: 40,
  },
});
