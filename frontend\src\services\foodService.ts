import { parseMultipartResponse, cleanupImageUrl } from '../utils/multipartResponse';
import { FoodItem } from '../types/food';
import { API_URL } from '../config';

export async function getFoodItem(id: string): Promise<{ foodItem: FoodItem; imageUrl: string | null }> {
  const response = await fetch(`${API_URL}/food/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch food item');
  }

  const { data: foodItem, imageUrl } = await parseMultipartResponse<FoodItem>(response);
  return { foodItem, imageUrl };
} 