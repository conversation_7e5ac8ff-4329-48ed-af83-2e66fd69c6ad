{"name": "kibbutz-app", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "dev:web": "expo start --web --dev-client", "dev:android": "expo start --android --dev-client", "dev:ios": "expo start --ios --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:web": "jest --config jest.web.config.js", "test:ios": "jest --config jest.ios.config.js", "test:android": "jest --config jest.android.config.js", "build:web": "expo export:web", "build:ios": "expo build:ios", "build:android": "expo build:android", "clean": "rm -rf node_modules && npm install"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-picker/picker": "^2.9.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@supabase/supabase-js": "^2.49.9", "expo": "~52.0.0", "expo-localization": "~16.0.1", "expo-notifications": "~0.29.14", "expo-status-bar": "~2.0.0", "i18next": "^23.0.0", "i18next-react-native-async-storage": "^1.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^13.0.0", "react-native": "0.76.9", "react-native-calendar-picker": "^8.0.5", "react-native-gesture-handler": "~2.20.0", "react-native-image-picker": "^7.1.0", "react-native-localize": "~3.0.0", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-vector-icons": "^10.0.3", "react-native-web": "~0.19.13"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.3.0", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-config-expo": "~8.0.1", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.7.0", "metro-react-native-babel-transformer": "^0.77.0", "prettier": "^3.0.0", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "^18.3.1", "typescript": "^5.3.3"}, "jest": {"preset": "react-native", "setupFiles": ["./src/test/setup.ts"], "transformIgnorePatterns": ["node_modules/(?!(jest-)?react-native|@react-native|@react-navigation)"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "private": true}