# Kibbutz Backend Installation Guide

## Prerequisites
- Docker Desktop for Windows
- Git for Windows
- PowerShell or Command Prompt

## Installation Steps

1. Clone the repository:
```powershell
git clone <repository-url>
cd backend
```

2. Create a `.env` file in the backend directory with the required environment variables (see Required Environment Variables section below).

3. Start the services:
```powershell
docker-compose up -d
```

4. Run the setup script to create the database and auth schema:
```powershell
.\scripts\setup.bat
```

5. Verify the installation:
```powershell
.\scripts\verify.bat
```

## Troubleshooting

### Database Connection Issues
- Ensure PostgreSQL container is running: `docker ps | findstr postgres`
- Check PostgreSQL logs: `docker logs kibbutz-postgres`
- Verify database exists: `docker exec kibbutz-postgres psql -U postgres -c "\l"`

### Service Startup Issues
- Check service logs: `docker logs kibbutz-auth`
- Verify environment variables: `type .env`
- Ensure all required services are running: `docker ps`

### Environment Variable Issues
- Check if `.env` file exists in the backend directory
- Verify all required variables are set
- Ensure no spaces around `=` signs in `.env` file

## Required Environment Variables
```env
POSTGRES_DB=kibbutz_db
POSTGRES_PASSWORD=postgres
JWT_SECRET=your-super-secret-jwt-token
ENABLE_EMAIL_SIGNUP=false
GOTRUE_EXTERNAL_EMAIL_ENABLED=false
```

## Manual Setup

If the setup script fails, you can manually create the database and schema:

```powershell
# Create database
docker exec -it kibbutz-postgres psql -U postgres -c "CREATE DATABASE kibbutz_db;"

# Create auth schema
docker exec -it kibbutz-postgres psql -U postgres -d kibbutz_db -c "CREATE SCHEMA IF NOT EXISTS auth;"
```

## Verification

To verify the installation:

1. Check if services are running:
```powershell
docker ps
```

2. Verify database and schema:
```powershell
docker exec kibbutz-postgres psql -U postgres -d kibbutz_db -c "\dn"
```

3. Check auth tables:
```powershell
docker exec kibbutz-postgres psql -U postgres -d kibbutz_db -c "\dt auth.*"
```

## Support

If you encounter issues:
1. Check the logs of the relevant service: `docker logs <service-name>`
2. Verify your environment variables: `type .env`
3. Ensure all containers are running: `docker ps`
4. Check the Supabase documentation for additional troubleshooting steps 