generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String         @id @default(uuid())
  community_id   String
  email          String         @unique
  password       String?
  role           UserRole
  user_type      UserType
  first_name     String?
  last_name      String?
  phone          String?
  language       String         @default("en")
  is_active      Boolean        @default(true)
  last_login     DateTime?
  google_id      String?
  created_at     DateTime?      @default(now())
  updated_at     DateTime       @updatedAt
  ui_preferences Json?          @default("{\"theme\": \"light\", \"spacing\": \"medium\", \"fontSize\": \"medium\", \"colorScheme\": \"default\", \"highContrast\": false}")
  chat_groups    ChatGroup[]
  messages       ChatMessage[]
  deals          Deal[]
  events         Event[]
  food_items     FoodItem[]
  jobs           Job[]
  refreshTokens  RefreshToken[]
  surveys        Survey[]

  @@index([community_id])
}

model Event {
  id           String    @id @default(uuid())
  community_id String
  title        String
  description  String
  date         DateTime
  location     String
  type         EventType
  image_url    String?
  data_ai_hint String?
  created_at   DateTime? @default(now())
  attendees    Int?      @default(0)
  created_by   String?
  icon         String?   @default("📅")
  user         User?     @relation(fields: [created_by], references: [id])

  @@index([community_id])
}

model FoodItem {
  id              String    @id @default(uuid())
  community_id    String
  name            String
  description     String
  price           String
  seller_name     String
  seller_id       String
  contact_info    String
  pickup_details  String?
  inline_photo_id String?
  data_ai_hint    String?
  created_at      DateTime? @default(now())
  seller          User      @relation(fields: [seller_id], references: [id])
  icon            String?   @default("🍲")
  icon_set        String?
  image_url       String?

  @@index([community_id])
}

model Job {
  id              String    @id @default(uuid())
  community_id    String
  title           String
  description     String
  location        String?
  date_time_info  String
  age_preference  String?
  skills          String[]
  status          JobStatus
  job_type        JobType
  posted_by_id    String
  posted_by_name  String
  inline_photo_id String?
  data_ai_hint    String?
  created_at      DateTime? @default(now())
  poster          User      @relation(fields: [posted_by_id], references: [id], onDelete: Cascade)

  @@index([community_id])
}

model Survey {
  id                 String       @id @default(uuid())
  community_id       String
  title              String
  description        String
  questions          Json
  status             SurveyStatus
  created_by         String
  created_at         DateTime?    @default(now())
  participants_count Int?         @default(0)
  closing_date       DateTime?
  image_url          String?
  data_ai_hint       String?
  creator            User         @relation(fields: [created_by], references: [id])

  @@index([community_id])
}

model Deal {
  id             String     @id @default(uuid())
  community_id   String
  name           String
  category       String
  description    String
  target_size    Int
  current_size   Int
  status         DealStatus
  organizer_id   String
  organizer_name String
  image_url      String?
  data_ai_hint   String?
  created_at     DateTime?  @default(now())
  participants   String[]
  organizer      User       @relation(fields: [organizer_id], references: [id], onDelete: Cascade)

  @@index([community_id])
}

model ChatGroup {
  id                     String        @id @default(uuid())
  community_id           String
  name                   String
  created_at             DateTime?     @default(now())
  created_by             String?
  members                String[]
  icon_url               String?
  data_ai_hint           String?
  last_message           String?
  last_message_timestamp DateTime?
  creator                User?         @relation(fields: [created_by], references: [id])
  messages               ChatMessage[] @relation("GroupMessages")

  @@index([community_id])
}

enum MessageType {
  TEXT
  IMAGE
}

model ChatMessage {
  id           String      @id @default(uuid())
  community_id String
  group_id     String
  sender_id    String?
  sender_name  String
  text         String
  type         MessageType @default(TEXT)
  image_url    String?
  timestamp    DateTime    @default(now())
  group        ChatGroup   @relation("GroupMessages", fields: [group_id], references: [id], onDelete: Cascade)
  sender       User?       @relation(fields: [sender_id], references: [id])

  @@index([community_id])
}

model RefreshToken {
  id         String    @id @default(uuid())
  token      String    @unique
  user_id    String
  expires_at DateTime
  created_at DateTime? @default(now())
  user       User      @relation(fields: [user_id], references: [id])

  @@index([user_id])
}

enum UserRole {
  ADMIN
  MODERATOR
  USER
}

enum UserType {
  ADULT
  YOUTH
  CHILD
  EXTERNAL
}

enum EventType {
  COMMUNITY
  TENANT
}

enum JobStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
}

enum JobType {
  OFFER
  REQUEST
}

enum SurveyStatus {
  OPEN
  CLOSED
}

enum DealStatus {
  ACTIVE
  COMPLETED
  CANCELLED
}
