import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useRoute, RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { FoodStackParamList } from '@navigation/types';
import { IconSet } from '@components';
import { FoodItem } from '../../types/food';
import { apiService } from '../../services/api';

type FoodDetailsScreenRouteProp = RouteProp<FoodStackParamList, 'FoodDetails'>;

export default function FoodDetailsScreen() {
  const { t } = useTranslation();
  const route = useRoute<FoodDetailsScreenRouteProp>();
  const { foodId } = route.params;
  const [foodItem, setFoodItem] = useState<FoodItem | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFoodItem();
  }, [foodId]);

  const loadFoodItem = async () => {
    try {
      const response = await apiService.getFoodItem(foodId);
      if (response.data) {
        setFoodItem(response.data);
      }
    } catch (error) {
      console.error('Error loading food item:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען פרטי מוצר...</Text>
      </View>
    );
  }

  if (!foodItem) {
    return (
      <View style={styles.centerContainer}>
        <Text>מוצר לא נמצא</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="🍲 פרטי המוצר"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.foodCard}>
          <View style={styles.foodIcon}>
            <Icon
              name={foodItem.icon || 'food'}
              iconSet={foodItem.iconSet || 'MaterialCommunityIcons'}
              size={24}
              color="#fff"
            />
          </View>

          <View style={styles.cardContent}>
            <Text style={styles.foodTitle}>{foodItem.name}</Text>
            <Text style={styles.foodDescription}>{foodItem.description}</Text>

            <View style={styles.foodMeta}>
              {foodItem.pickupDetails && (
                <Text style={styles.metaText}>📍 {foodItem.pickupDetails}</Text>
              )}
              <Text style={styles.metaText}>👤 {foodItem.sellerName}</Text>
            </View>
          </View>

          <View style={styles.priceTag}>
            <Text style={styles.priceText}>₪{foodItem.price}</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.contactButton}>
          <Text style={styles.contactButtonText}>📞 צור קשר</Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  foodCard: {
    flexDirection: 'row',
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  foodIcon: {
    width: 50,
    height: 50,
    borderRadius: getBorderRadius('lg'),
    backgroundColor: '#f9ca24',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getSpacing('lg'),
  },
  cardContent: {
    flex: 1,
  },
  foodTitle: {
    ...getTypography('lg', 'bold'),
    color: getColor('primary'),
    marginBottom: getSpacing('xs'),
  },
  foodDescription: {
    ...getTypography('sm'),
    color: getColor('neutral', 600),
    marginBottom: getSpacing('sm'),
  },
  foodMeta: {
    flexDirection: 'row',
    gap: getSpacing('md'),
  },
  metaText: {
    ...getTypography('sm'),
    color: getColor('neutral', 500),
  },
  priceTag: {
    backgroundColor: '#27ae60',
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('xs'),
    marginLeft: getSpacing('md'),
  },
  priceText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  contactButton: {
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('full'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  contactButtonText: {
    color: getColor('white'),
    ...getTypography('base', 'bold'),
  },
}); 