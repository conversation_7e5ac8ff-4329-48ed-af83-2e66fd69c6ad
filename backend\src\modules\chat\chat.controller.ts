import { Request, Response } from 'express';
import { ChatService } from './chat.service';
import { getModuleLogger } from '../../utils/logger';
import { ForbiddenError, NotFoundError } from '../../utils/errors';
import { supabase } from '../../lib/supabase';

const logger = getModuleLogger('Chat');

export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  async createChatGroup(req: Request, res: Response): Promise<void> {
    try {
      logger.debug('POST /chat/groups - Request received:', { body: req.body });
      const chatGroup = await this.chatService.createChatGroup(req.body);
      logger.debug('Response body:', chatGroup);
      res.status(201).json(chatGroup);
    } catch (error) {
      logger.error('Error creating chat group:', { error });
      res.status(500).json({ message: 'Error creating chat group', error });
    }
  }

  async getChatGroups(_req: Request, res: Response): Promise<void> {
    try {
      logger.debug('GET /chat/groups - Request received');
      const chatGroups = await this.chatService.getAllChatGroups();
      logger.debug('Response body:', { count: chatGroups.length });
      res.json(chatGroups);
    } catch (error) {
      logger.error('Error fetching chat groups:', { error });
      res.status(500).json({ message: 'Error fetching chat groups', error });
    }
  }

  async getChatGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        logger.debug('Missing id parameter');
        res.status(400).json({ message: 'Missing id parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('GET /chat/groups/:id - Request received:', { id });
      const chatGroup = await this.chatService.getChatGroup(id, userId);
      logger.debug('Response body:', chatGroup);
      res.json(chatGroup);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error fetching chat group:', { error, id: req.params['id'] });
        res.status(500).json({ message: 'Error fetching chat group', error });
      }
    }
  }

  async updateChatGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        logger.debug('Missing id parameter');
        res.status(400).json({ message: 'Missing id parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('PUT /chat/groups/:id - Request received:', { id, body: req.body });
      const updatedGroup = await this.chatService.updateChatGroup(id, req.body, userId);
      logger.debug('Response body:', updatedGroup);
      res.json(updatedGroup);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error updating chat group:', { error, id: req.params['id'] });
        res.status(500).json({ message: 'Error updating chat group', error });
      }
    }
  }

  async deleteChatGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        logger.debug('Missing id parameter');
        res.status(400).json({ message: 'Missing id parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('DELETE /chat/groups/:id - Request received:', { id });
      await this.chatService.deleteChatGroup(id, userId);
      logger.debug('Chat group deleted successfully:', { id });
      res.status(204).send();
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error deleting chat group:', { error, id: req.params['id'] });
        res.status(500).json({ message: 'Error deleting chat group', error });
      }
    }
  }

  async createChatMessage(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('POST /chat/messages - Request received:', { body: req.body });
      
      // Map request body fields to match service expectations
      const messageData = {
        groupId: req.body.group_id,
        community_id: req.body.community_id,
        senderId: req.body.sender_id,
        senderName: req.body.sender_name,
        text: req.body.text,
        timestamp: new Date()
      };

      const message = await this.chatService.createChatMessage(messageData, userId);
      logger.debug('Response body:', message);
      res.status(201).json(message);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error creating chat message:', { error });
        res.status(500).json({ message: 'Error creating chat message', error });
      }
    }
  }

  async getChatMessages(_req: Request, res: Response): Promise<void> {
    try {
      logger.debug('GET /chat/messages - Request received');
      const chatMessages = await this.chatService.getAllChatMessages();
      logger.debug('Response body:', { count: chatMessages.length });
      res.json(chatMessages);
    } catch (error) {
      logger.error('Error fetching chat messages:', { error });
      res.status(500).json({ message: 'Error fetching chat messages', error });
    }
  }

  async getChatMessagesByGroupId(req: Request, res: Response): Promise<void> {
    try {
      const { groupId } = req.params;
      const userId = req.user?.id;
      if (!groupId) {
        logger.debug('Missing groupId parameter');
        res.status(400).json({ message: 'Missing groupId parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('GET /chat/groups/:groupId/messages - Request received:', { groupId });
      const chatMessages = await this.chatService.getChatMessagesByGroupId(groupId, userId);
      logger.debug('Response body:', { count: chatMessages.length });
      res.json(chatMessages);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error fetching chat messages by group:', { error, groupId: req.params['groupId'] });
        res.status(500).json({ message: 'Error fetching chat messages', error });
      }
    }
  }

  async getChatMessage(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        logger.debug('Missing id parameter');
        res.status(400).json({ message: 'Missing id parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('GET /chat/messages/:id - Request received:', { id });
      const message = await this.chatService.getChatMessage(id, userId);
      logger.debug('Response body:', message);
      res.json(message);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error fetching chat message:', { error, id: req.params['id'] });
        res.status(500).json({ message: 'Error fetching chat message', error });
      }
    }
  }

  async updateChatMessage(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        logger.debug('Missing id parameter');
        res.status(400).json({ message: 'Missing id parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('PUT /chat/messages/:id - Request received:', { id, body: req.body });
      const updatedMessage = await this.chatService.updateChatMessage(id, req.body, userId);
      logger.debug('Response body:', updatedMessage);
      res.json(updatedMessage);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error updating chat message:', { error, id: req.params['id'] });
        res.status(500).json({ message: 'Error updating chat message', error });
      }
    }
  }

  async deleteChatMessage(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        logger.debug('Missing id parameter');
        res.status(400).json({ message: 'Missing id parameter' });
        return;
      }
      if (!userId) {
        logger.debug('Missing user ID');
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      logger.debug('DELETE /chat/messages/:id - Request received:', { id });
      await this.chatService.deleteChatMessage(id, userId);
      logger.debug('Chat message deleted successfully:', { id });
      res.status(204).send();
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({ message: error.message });
      } else if (error instanceof ForbiddenError) {
        res.status(403).json({ message: error.message });
      } else {
        logger.error('Error deleting chat message:', { error, id: req.params['id'] });
        res.status(500).json({ message: 'Error deleting chat message', error });
      }
    }
  }

  async createImageMessage(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }
      const file = req.file;
      if (!file) {
        res.status(400).json({ message: 'No file uploaded' });
        return;
      }
      // Upload to Supabase Storage
      const fileName = `${Date.now()}-${file.originalname}`;
      const { data, error } = await supabase.storage
        .from('kibbutz-storage')
        .upload(fileName, file.buffer, { contentType: file.mimetype });
      if (error) {
        console.error('Supabase upload error:', error);
        res.status(500).json({ message: 'Failed to upload image', error });
        return;
      }
      const { data: publicUrlData } = supabase.storage
        .from('kibbutz-storage')
        .getPublicUrl(data.path);
      const imageUrl = publicUrlData.publicUrl;
      // Create chat message of type IMAGE
      const messageData = {
        groupId: req.body.group_id,
        community_id: req.body.community_id,
        senderId: req.body.sender_id,
        senderName: req.body.sender_name,
        text: '',
        type: 'IMAGE',
        image_url: imageUrl,
        timestamp: new Date(),
      };
      const message = await this.chatService.createChatMessage(messageData, userId);
      res.status(201).json(message);
    } catch (error) {
      console.error('createImageMessage error:', error);
      res.status(500).json({ message: 'Error uploading image message', error });
    }
  }
} 