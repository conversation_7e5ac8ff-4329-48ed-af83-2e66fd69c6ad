interface ErrorReport {
  error: Error;
  context?: string;
  userId?: string;
  timestamp: Date;
  platform: string;
  appVersion?: string;
  additionalData?: Record<string, any>;
}

class ErrorReportingService {
  private isEnabled: boolean = !__DEV__;
  private reports: ErrorReport[] = [];

  /**
   * Report an error to the logging service
   */
  reportError(
    error: Error, 
    context?: string, 
    additionalData?: Record<string, any>
  ): void {
    const report: ErrorReport = {
      error,
      context,
      timestamp: new Date(),
      platform: this.getPlatform(),
      additionalData,
    };

    // Store locally for debugging
    this.reports.push(report);

    // Log to console in development
    if (__DEV__) {
      console.group(`🚨 Error Report${context ? ` - ${context}` : ''}`);
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Platform:', report.platform);
      console.log('Timestamp:', report.timestamp);
      if (additionalData) {
        console.log('Additional Data:', additionalData);
      }
      console.groupEnd();
    }

    // In production, send to external service
    if (this.isEnabled) {
      this.sendToExternalService(report);
    }
  }

  /**
   * Report a React Error Boundary error
   */
  reportBoundaryError(
    error: Error, 
    errorInfo: React.ErrorInfo,
    context?: string
  ): void {
    this.reportError(error, context || 'ErrorBoundary', {
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    });
  }

  /**
   * Get recent error reports (for debugging)
   */
  getRecentReports(limit: number = 10): ErrorReport[] {
    return this.reports.slice(-limit);
  }

  /**
   * Clear stored reports
   */
  clearReports(): void {
    this.reports = [];
  }

  /**
   * Enable/disable error reporting
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  private getPlatform(): string {
    if (typeof window !== 'undefined') {
      return 'web';
    }
    // In React Native, you can use Platform.OS
    return 'mobile';
  }

  private async sendToExternalService(report: ErrorReport): Promise<void> {
    try {
      // Example: Send to Sentry, LogRocket, or custom endpoint
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(report),
      // });
      
      console.log('Error report would be sent to external service:', report);
    } catch (sendError) {
      console.warn('Failed to send error report:', sendError);
    }
  }
}

// Export singleton instance
export const errorReportingService = new ErrorReportingService();

// Export types for use in other files
export type { ErrorReport };
