services:
  # PostgreSQL Database
  db:
    container_name: kibbutz-postgres
    image: postgres:15-alpine
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: postgres
    volumes:
      - ./volumes/db:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MinIO Object Storage
  minio:
    container_name: kibbutz-minio
    image: minio/minio:latest
    restart: unless-stopped
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin}
    volumes:
      - ./volumes/minio:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # PostgREST API
  rest:
    container_name: kibbutz-rest
    image: postgrest/postgrest:v12.0.2
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      PGRST_DB_URI: postgres://postgres:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
    depends_on:
      db:
        condition: service_healthy

  # GoTrue Auth Service
  auth:
    container_name: kibbutz-auth
    image: supabase/gotrue:v2.143.0
    restart: unless-stopped
    ports:
      - "9999:9999"
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: ${API_EXTERNAL_URL}
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://postgres:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}?search_path=auth
      GOTRUE_SITE_URL: ${SITE_URL}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS}
      GOTRUE_DISABLE_SIGNUP: ${DISABLE_SIGNUP}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: ${JWT_EXPIRY}
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: ${ENABLE_EMAIL_SIGNUP}
      GOTRUE_MAILER_AUTOCONFIRM: ${ENABLE_EMAIL_AUTOCONFIRM}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SMTP_ADMIN_EMAIL}
      GOTRUE_SMTP_HOST: ${SMTP_HOST}
      GOTRUE_SMTP_PORT: ${SMTP_PORT}
      GOTRUE_SMTP_USER: ${SMTP_USER}
      GOTRUE_SMTP_PASS: ${SMTP_PASS}
      GOTRUE_SMTP_SENDER_NAME: ${SMTP_SENDER_NAME}
      GOTRUE_MAILER_URLPATHS_INVITE: ${MAILER_URLPATHS_INVITE}
      GOTRUE_MAILER_URLPATHS_CONFIRMATION: ${MAILER_URLPATHS_CONFIRMATION}
      GOTRUE_MAILER_URLPATHS_RECOVERY: ${MAILER_URLPATHS_RECOVERY}
      GOTRUE_MAILER_URLPATHS_EMAIL_CHANGE: ${MAILER_URLPATHS_EMAIL_CHANGE}
    depends_on:
      db:
        condition: service_healthy

  # Realtime Service
  realtime:
    container_name: kibbutz-realtime
    image: supabase/realtime:v2.25.50
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      PORT: 4000
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_NAME: ${POSTGRES_DB}
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: ${JWT_SECRET}
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: "false"
      DNS_NODES: "''"
    depends_on:
      db:
        condition: service_healthy

  # Storage Service
  storage:
    container_name: kibbutz-storage
    image: supabase/storage-api:v1.24.6
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      ENABLE_ADMIN_API: "true"
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SERVICE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      POSTGREST_URL: http://rest:3000
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgres://postgres:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: kibbutz
      REGION: local
      GLOBAL_S3_BUCKET: kibbutz
      ENABLE_IMAGE_TRANSFORMATION: "true"
      IMGPROXY_URL: http://imgproxy:5001
      PROJECT_REF: kibbutz
      JWT_SECRET: ${JWT_SECRET}
    volumes:
      - ./volumes/storage:/var/lib/storage
    depends_on:
      db:
        condition: service_healthy
    networks:
      - supabase_internal

  # Image Proxy for Storage
  imgproxy:
    container_name: kibbutz-imgproxy
    image: darthsim/imgproxy:v3.8.0
    restart: unless-stopped
    ports:
      - "5001:8080"
    environment:
      IMGPROXY_BIND: ":8080"
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: "true"
      IMGPROXY_ENABLE_WEBP_DETECTION: ${IMGPROXY_ENABLE_WEBP_DETECTION}
    volumes:
      - ./volumes/storage:/var/lib/storage

  # Kong API Gateway
  kong:
    container_name: kibbutz-kong
    image: kong:2.8-alpine
    restart: unless-stopped
    ports:
      - "8000:8000/tcp"
      - "8443:8443/tcp"
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
      KONG_CORS_ORIGINS: '*'
      KONG_CORS_CREDENTIALS: 'true'
      KONG_CORS_MAX_AGE: 3600
      KONG_CORS_METHODS: 'GET, POST, PUT, DELETE, OPTIONS'
      KONG_CORS_HEADERS: 'Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, Authorization, apikey, X-Client-Info, x-supabase-api-version, x-supabase-client, x-supabase-auth, x-supabase-anon-key, x-supabase-service-role-key, x-supabase-access-token'
      KONG_CORS_EXPOSED_HEADERS: 'Content-Length, Content-Range'
    volumes:
      - ./volumes/kong:/var/lib/kong

  # Supabase Studio
  studio:
    container_name: kibbutz-studio
    image: supabase/studio:20240326-5e5586d
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      STUDIO_PG_META_URL: http://meta:8080
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      DEFAULT_ORGANIZATION_NAME: ${STUDIO_DEFAULT_ORGANIZATION}
      DEFAULT_PROJECT_NAME: ${STUDIO_DEFAULT_PROJECT}
      SUPABASE_PUBLIC_URL: ${SUPABASE_PUBLIC_URL}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}

  # Meta Service for Studio
  meta:
    container_name: kibbutz-meta
    image: supabase/postgres-meta:v0.75.0
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: db
      PG_META_DB_PORT: 5432
      PG_META_DB_NAME: ${POSTGRES_DB}
      PG_META_DB_USER: postgres
      PG_META_DB_PASSWORD: ${POSTGRES_PASSWORD}
    depends_on:
      db:
        condition: service_healthy

  # Elasticsearch
  elasticsearch:
    container_name: kibbutz-elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.1
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=${ELASTIC_SECURITY_ENABLED:-false}
      - "ES_JAVA_OPTS=-Xms${ELASTIC_HEAP_SIZE:-512}m -Xmx${ELASTIC_HEAP_SIZE:-512}m"
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-changeme}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - ${ELASTIC_DATA_PATH}:/usr/share/elasticsearch/data
    ports:
      - "${ELASTIC_PORT:-9200}:9200"
    networks:
      - elastic
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:9200 >/dev/null || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana
  kibana:
    container_name: kibbutz-kibana
    image: docker.elastic.co/kibana/kibana:8.12.1
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=${KIBANA_USERNAME:-kibana_system}
      - ELASTICSEARCH_PASSWORD=${ELASTIC_PASSWORD:-changeme}
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    networks:
      - elastic
    depends_on:
      elasticsearch:
        condition: service_healthy

  # Filebeat
  filebeat:
    container_name: kibbutz-filebeat
    image: docker.elastic.co/beats/filebeat:8.12.1
    user: root
    volumes:
      - ${FILEBEAT_CONFIG_PATH}:/usr/share/filebeat/filebeat.yml
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - elastic
    depends_on:
      elasticsearch:
        condition: service_healthy
    command: >
      bash -c "chmod go-w /usr/share/filebeat/filebeat.yml && filebeat -e"

networks:
  elastic:
    driver: bridge
  supabase_internal:
    name: supabase_internal

