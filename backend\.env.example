# Environment Variables

# Required Variables
# PostgreSQL database connection string
DATABASE_URL=postgresql://user:password@localhost:5432/kibbutz_db?schema=public

# Supabase project URL
SUPABASE_PUBLIC_URL=

# Supabase anonymous key
SUPABASE_ANON_KEY=


# Optional Variables

# Database

# Logging
# Global logging level (fatal, error, warn, info, debug)
LOG_LEVEL=info

# Enable debug logging for logger configuration
LOG_DEBUG=false

# Job module logging level
LOG_LEVEL_JOB=info

# Auth module logging level
LOG_LEVEL_AUTH=info

# Maintenance module logging level
LOG_LEVEL_MAINTENANCE=info

# Common module logging level
LOG_LEVEL_COMMON=info

# Supabase module logging level
LOG_LEVEL_SUPABASE=info

# Database module logging level
LOG_LEVEL_DATABASE=info


# Supabase
# Supabase service role key
SUPABASE_SERVICE_ROLE_KEY=

# Supabase JWT secret
SUPABASE_JWT_SECRET=

# Supabase auth token expiry in seconds
SUPABASE_AUTH_EXPIRY=3600

# Supabase auth refresh token expiry in seconds
SUPABASE_AUTH_REFRESH_EXPIRY=604800

# Supabase auth cookie name
SUPABASE_AUTH_COOKIE_NAME=sb-auth-token

# Supabase auth cookie domain
SUPABASE_AUTH_COOKIE_DOMAIN=

# Whether to use secure cookies
SUPABASE_AUTH_COOKIE_SECURE=true

# Supabase auth cookie same-site policy
SUPABASE_AUTH_COOKIE_SAME_SITE=lax


# Server
# Port number for the server
PORT=3002

# Environment (development, production, test)
NODE_ENV=development

# Frontend URL for CORS configuration
FRONTEND_URL=http://localhost:3000

# Rate limiting window in milliseconds
RATE_LIMIT_WINDOW_MS=900000

# Maximum number of requests per window
RATE_LIMIT_MAX_REQUESTS=100

