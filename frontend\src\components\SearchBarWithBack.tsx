import React from 'react';
import {
  View,
  TextInput,
  StyleSheet,
} from 'react-native';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../theme';

interface SearchBarWithBackProps {
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
}

export default function SearchBarWithBack({
  placeholder,
  value,
  onChangeText
}: SearchBarWithBackProps) {
  return (
    <View style={styles.container}>
      <TextInput
        style={styles.searchBar}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        placeholderTextColor={getColor('neutral', 500)}
        textAlign="right"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: getSpacing('lg'),
  },
  searchBar: {
    width: '100%',
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('full'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('md'),
    ...getTypography('base'),
    ...getShadow('sm'),
  },
});
