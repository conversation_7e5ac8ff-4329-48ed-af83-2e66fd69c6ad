import { createClient } from '@supabase/supabase-js';
import { getModuleLogger } from '../utils/logger';
import request from 'supertest';
import { app } from '../app';

const logger = getModuleLogger('Auth');

// Initialize Supabase client
const supabaseUrl = process.env['SUPABASE_PUBLIC_URL'] || 'http://localhost:54321';
const supabaseKey = process.env['SUPABASE_ANON_KEY'] || 'your-anon-key';
const supabase = createClient(supabaseUrl, supabaseKey);

export class SupabaseAuthWrapper {
  private user: any = null;
  private token: string | null = null;
  private email: string | null = null;
  private password: string = 'Test123!@#';

  async signUp(): Promise<{ user: any; token: string }> {
    const random = Math.random().toString(36).substring(2, 8);
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    this.email = `test-${random}@example.com`;
    this.password = `Test123!@#-${timestamp}`;

    // Sign up in Supabase
    const { data: authData, error: authError } = await supabase.auth.signUp({ 
      email: this.email, 
      password: this.password,
      options: {
        data: {
          role: "MODERATOR",
        },
      }
    });
    if (authError) throw authError;
    if (!authData.user) throw new Error('No user returned from signUp');

    // Add a small delay to ensure Supabase user is fully created
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create user profile via public API
    const userData = {
      id: authData.user.id,
      email: this.email,
      firstName: 'Test',
      lastName: 'User',
      role: 'MODERATOR',
      userType: 'ADULT',
      language: 'en'
    };

    const response = await request(app)
      .post('/api/users')
      .set('Authorization', `Bearer ${authData.session?.access_token}`)
      .send(userData);

    if (response.status !== 201) {
      throw new Error(`Failed to create user profile: ${response.body.error}`);
    }

    this.user = response.body.data;
    this.token = authData.session?.access_token || '';
    
    logger.debug(`SupabaseAuthWrapper: User signed up - email: ${this.email}, userId: ${authData.user.id}`);

    return { user: this.user, token: this.token };
  }

  async signIn(email: string, password: string): Promise<{ user: any; token: string }> {
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) throw error;
    if (!data.user) throw new Error('No user returned from signIn');

    // Get user profile via public API
    const response = await request(app)
      .get(`/api/users/${data.user.id}`)
      .set('Authorization', `Bearer ${data.session?.access_token}`);

    if (response.status !== 200) {
      throw new Error(`Failed to get user profile: ${response.body.error}`);
    }

    this.user = response.body.data;
    this.token = data.session?.access_token || '';
    this.email = email;
    
    logger.debug(`SupabaseAuthWrapper: User signed in - email: ${email}, userId: ${data.user.id}`);

    return { user: this.user, token: this.token };
  }

  async signOut(): Promise<void> {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    this.user = null;
    this.token = null;
    this.email = null;
    logger.debug('SupabaseAuthWrapper: User signed out');
  }

  async deleteUser(): Promise<void> {
    if (!this.user) {
      logger.debug('No user to delete');
      return;
    }

    try {
      // Only delete from Supabase Auth using service role key
      const serviceRoleKey = process.env['SUPABASE_SERVICE_ROLE_KEY'];
      logger.debug(`Service role key (first 8 chars): ${serviceRoleKey ? serviceRoleKey.slice(0, 8) : 'not set'}`);
      logger.debug(`Supabase URL: ${supabaseUrl}`);
      logger.debug(`Deleting Supabase user ID: ${String(this.user.id)}`);

      if (!serviceRoleKey) {
        logger.warn('SUPABASE_SERVICE_ROLE_KEY is not set, skipping Supabase user deletion');
        throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set');
      }

      const adminClient = createClient(supabaseUrl, serviceRoleKey);
      const { data: { user: supabaseUser }, error: getUserError } = await adminClient.auth.admin.getUserById(this.user.id);
      logger.debug(`Supabase getUserById: user=${supabaseUser ? JSON.stringify(supabaseUser) : 'null'}, error=${getUserError ? getUserError.message : 'no error'}`);

      if (getUserError) {
        if (getUserError.message?.includes('User not found')) {
          logger.debug('User already deleted from Supabase');
        } else {
          logger.error(`Error getting user from Supabase: ${getUserError.message}`);
          throw getUserError;
        }
      }

      if (supabaseUser) {
        const { error: deleteError } = await adminClient.auth.admin.deleteUser(supabaseUser.id);
        logger.debug(`Supabase deleteUser error: ${deleteError ? deleteError.message : 'no error'}`);
        if (deleteError) {
          if (deleteError.message?.includes('User not found')) {
            logger.debug('User already deleted from Supabase');
          } else {
            logger.error(`Error deleting user from Supabase: ${deleteError.message}`);
            throw deleteError;
          }
        }
      }

      this.user = null;
      this.token = null;
      this.email = null;

      logger.debug('Successfully deleted user from Supabase');
    } catch (error) {
      logger.error(`Error in deleteUser: ${error instanceof Error ? error.message : error}`);
      throw error;
    }
  }

  getUserToken(): string {
    if (!this.token) {
      throw new Error('No token available. User may not be authenticated.');
    }
    return this.token;
  }

  getCurrentUser(): any {
    if (!this.user) {
      throw new Error('No user available. User may not be authenticated.');
    }
    return this.user;
  }
}