import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger';
import { Bucket } from '@supabase/storage-js';

export interface BucketConfig {
  name: string;
  public: boolean;
  fileSizeLimit?: number;
  allowedMimeTypes?: string[];
}

export interface BucketResponse {
  id: string;
  name: string;
  owner: string;
  public: boolean;
  file_size_limit: number | null;
  allowed_mime_types: string[] | null;
  created_at: string;
  updated_at: string;
}

function mapBucketToResponse(bucket: Bucket): BucketResponse {
  return {
    id: bucket.id,
    name: bucket.name,
    owner: bucket.owner,
    public: bucket.public,
    file_size_limit: bucket.file_size_limit ?? null,
    allowed_mime_types: bucket.allowed_mime_types ?? null,
    created_at: bucket.created_at,
    updated_at: bucket.updated_at
  };
}

export interface UploadOptions {
  cacheControl?: string;
  upsert?: boolean;
  contentType?: string;
}

export interface UploadResponse {
  path: string;
  fullPath: string;
}

export interface DownloadResponse {
  data: Buffer | null;
  error: any;
}

export interface FileObject {
  name: string;
  id: string;
  updated_at: string;
  created_at: string;
  last_accessed_at: string;
  metadata: Record<string, any>;
}

export interface ListFilesOptions {
  limit?: number;
  offset?: number;
  sortBy?: {
    column: 'name' | 'created_at' | 'updated_at' | 'last_accessed_at';
    order: 'asc' | 'desc';
  };
}

export interface ListFilesResponse {
  data: FileObject[] | null;
  error: any;
}

export interface DeleteFilesResponse {
  data: any[] | null;
  error: any;
}

interface StorageResponse<T> {
  data: T | null;
  error: Error | null;
}

export class StorageClient {
  private static instance: StorageClient;
  private supabase;
  private initialized = false;
  private readonly buckets: BucketConfig[] = [
    {
      name: 'food-images',
      public: true,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif']
    }
  ];

  private constructor() {
    if (!process.env['SUPABASE_STORAGE_URL'] || !process.env['SUPABASE_SERVICE_ROLE_KEY']) {
      logger.error('Missing required environment variables for storage service');
      throw new Error('Missing required environment variables for storage service');
    }

    logger.info(`Initializing Supabase client with URL: ${process.env['SUPABASE_STORAGE_URL']}`);
    this.supabase = createClient(
      process.env['SUPABASE_STORAGE_URL'],
      process.env['SUPABASE_SERVICE_ROLE_KEY'],
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    );
  }

  public static getInstance(): StorageClient {
    if (!StorageClient.instance) {
      StorageClient.instance = new StorageClient();
    }
    return StorageClient.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.info('Storage service already initialized');
      return;
    }

    try {
      logger.info('Starting storage service initialization...');
      // Create buckets if they don't exist
      for (const bucket of this.buckets) {
        logger.info(`Initializing bucket: ${bucket.name}`);
        await this.createBucket(bucket);
      }
      this.initialized = true;
      logger.info('Storage service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize storage service:', error);
      throw error;
    }
  }

  public async createBucket(config: BucketConfig): Promise<{ data: BucketResponse | null; error: any }> {
    try {
      logger.info(`Checking if bucket '${config.name}' exists...`);
      
      // List existing buckets using Supabase API
      const { data: existingBuckets, error: listError } = await this.supabase.storage.listBuckets();

      if (listError) {
        logger.error(`Failed to list buckets:`, listError);
        throw new Error(`Failed to list buckets: ${listError.message}`);
      }

      if (!existingBuckets.some((b: Bucket) => b.name === config.name)) {
        logger.info(`Creating bucket '${config.name}'...`);
        
        const { data, error } = await this.supabase.storage.createBucket(config.name, {
          public: config.public,
          fileSizeLimit: config.fileSizeLimit ?? null,
          allowedMimeTypes: config.allowedMimeTypes ?? null
        });

        if (error) {
          logger.error(`Failed to create bucket '${config.name}':`, error);
          throw new Error(`Failed to create bucket ${config.name}: ${error.message}`);
        }

        logger.info(`Successfully created bucket: ${config.name}`);
        return { 
          data: mapBucketToResponse(data as Bucket),
          error: null 
        };
      } else {
        logger.info(`Bucket '${config.name}' already exists`);
        const bucket = existingBuckets.find((b: Bucket) => b.name === config.name);
        if (bucket) {
          return { 
            data: mapBucketToResponse(bucket as Bucket),
            error: null 
          };
        }
        return { data: null, error: null };
      }
    } catch (error) {
      logger.error(`Error managing bucket '${config.name}':`, error);
      throw error;
    }
  }

  public async uploadFile(
    bucketName: string,
    filePath: string,
    file: Buffer,
    options: UploadOptions = {}
  ): Promise<{ data: UploadResponse | null; error: any }> {
    try {
      logger.info(`Uploading file to bucket '${bucketName}' at path '${filePath}'...`);
      
      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          ...(options.cacheControl && { cacheControl: options.cacheControl }),
          ...(options.upsert && { upsert: options.upsert }),
          ...(options.contentType && { contentType: options.contentType })
        });

      if (error) {
        logger.error(`Failed to upload file to '${filePath}':`, error);
        throw new Error(`Failed to upload file: ${error.message}`);
      }

      logger.info(`Successfully uploaded file to '${filePath}'`);
      return {
        data: {
          path: data.path,
          fullPath: `${bucketName}/${data.path}`
        },
        error: null
      };
    } catch (error) {
      logger.error(`Error uploading file to '${filePath}':`, error);
      throw error;
    }
  }

  public async uploadFoodImage(foodId: string, file: Buffer, mimeType: string): Promise<string> {
    try {
      logger.info(`[FOOD_IMAGE] Starting upload for food ID: ${foodId}`);
      
      // Generate a unique filename
      const fileName = `${foodId}-${Date.now()}.${mimeType.split('/')[1]}`;
      logger.info(`[FOOD_IMAGE] Generated filename: ${fileName}`);

      // Upload to food-images bucket
      const { data, error } = await this.uploadFile('food-images', fileName, file, {
        contentType: mimeType,
        upsert: true
      });

      if (error) {
        logger.error('[FOOD_IMAGE] Upload failed:', error);
        throw new Error(`Failed to upload food image: ${error.message}`);
      }

      // Get the public URL for the uploaded image
      const publicUrl = this.getPublicUrl('food-images', data?.path ?? '');
      logger.info(`[FOOD_IMAGE] Successfully uploaded image. Public URL: ${publicUrl}`);
      
      return publicUrl;
    } catch (error) {
      logger.error('[FOOD_IMAGE] Error in uploadFoodImage:', error);
      throw error;
    }
  }

  public async getFoodImage(path: string): Promise<Buffer | null> {
    try {
      logger.info(`[FOOD_IMAGE] Getting image from path: ${path}`);
      const { data, error } = await this.downloadFile('food-images', path);
      
      if (error) {
        logger.error('[FOOD_IMAGE] Failed to get image:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('[FOOD_IMAGE] Error getting food image:', error);
      return null;
    }
  }

  public async deleteFoodImage(path: string): Promise<void> {
    try {
      logger.info(`[FOOD_IMAGE] Deleting image at path: ${path}`);
      
      // Extract filename from path if it's a full URL
      const fileName = path.split('/').pop();
      if (!fileName) {
        throw new Error('Invalid image path');
      }

      const { error } = await this.deleteFiles('food-images', [fileName]);
      if (error) {
        logger.error('[FOOD_IMAGE] Failed to delete image:', error);
        throw new Error(`Failed to delete food image: ${error.message}`);
      }

      logger.info(`[FOOD_IMAGE] Successfully deleted image: ${fileName}`);
    } catch (error) {
      logger.error('[FOOD_IMAGE] Error in deleteFoodImage:', error);
      throw error;
    }
  }

  public getPublicUrl(bucket: string, path: string): string {
    const { data } = this.supabase.storage
      .from(bucket)
      .getPublicUrl(path);
    return data.publicUrl;
  }

  public async getBucket(bucketName: string): Promise<{ data: BucketResponse | null; error: any }> {
    try {
      logger.info(`Getting bucket '${bucketName}'...`);
      
      const { data: buckets, error: listError } = await this.supabase.storage.listBuckets();
      if (listError) {
        logger.error(`Failed to list buckets:`, listError);
        throw new Error(`Failed to list buckets: ${listError.message}`);
      }

      const bucket = buckets.find((b: Bucket) => b.name === bucketName);
      if (!bucket) {
        logger.info(`Bucket '${bucketName}' not found`);
        return { data: null, error: { message: 'Bucket not found' } };
      }

      logger.info(`Successfully got bucket '${bucketName}'`);
      return {
        data: mapBucketToResponse(bucket as Bucket),
        error: null
      };
    } catch (error) {
      logger.error(`Error getting bucket '${bucketName}':`, error);
      throw error;
    }
  }

  public async listBuckets(): Promise<{ data: BucketResponse[] | null; error: any }> {
    try {
      logger.info('Listing all buckets...');
      
      const { data: buckets, error } = await this.supabase.storage.listBuckets();
      if (error) {
        logger.error('Failed to list buckets:', error);
        throw new Error(`Failed to list buckets: ${error.message}`);
      }

      logger.info(`Successfully listed ${buckets.length} buckets`);
      return {
        data: buckets.map((bucket: Bucket) => mapBucketToResponse(bucket)),
        error: null
      };
    } catch (error) {
      logger.error('Error listing buckets:', error);
      throw error;
    }
  }

  public async deleteBucket(bucketName: string): Promise<{ data: { message: string } | null; error: any }> {
    try {
      logger.info(`Deleting bucket '${bucketName}'...`);
      
      // First empty the bucket
      await this.emptyBucket(bucketName);
      
      // Then delete the bucket
      const { error } = await this.supabase.storage.deleteBucket(bucketName);
      if (error) {
        logger.error(`Failed to delete bucket '${bucketName}':`, error);
        throw new Error(`Failed to delete bucket: ${error.message}`);
      }

      logger.info(`Successfully deleted bucket '${bucketName}'`);
      return {
        data: { message: 'Successfully deleted' },
        error: null
      };
    } catch (error) {
      logger.error(`Error deleting bucket '${bucketName}':`, error);
      throw error;
    }
  }

  public async emptyBucket(bucketName: string): Promise<{ data: { message: string } | null; error: any }> {
    try {
      logger.info(`[EMPTY_BUCKET] Starting to empty bucket '${bucketName}'...`);
      
      // List all files in the bucket
      logger.info(`[EMPTY_BUCKET] Listing files in bucket '${bucketName}'...`);
      const { data: files, error: listError } = await this.listFiles(bucketName);
      if (listError) {
        logger.error(`[EMPTY_BUCKET] Failed to list files in bucket '${bucketName}':`, listError);
        throw listError;
      }

      logger.info(`[EMPTY_BUCKET] Found ${files?.length || 0} files in bucket '${bucketName}'`);
      
      if (files && files.length > 0) {
        // Delete all files
        const filePaths = files.map(file => file.name);
        logger.info(`[EMPTY_BUCKET] Deleting ${filePaths.length} files from bucket '${bucketName}':`, filePaths);
        const deleteResponse = await this.deleteFiles(bucketName, filePaths);
        logger.info(`[EMPTY_BUCKET] Delete files response:`, deleteResponse);
      }

      // Verify bucket is empty
      logger.info(`[EMPTY_BUCKET] Verifying bucket '${bucketName}' is empty...`);
      const { data: remainingFiles, error: verifyError } = await this.listFiles(bucketName);
      if (verifyError) {
        logger.error(`[EMPTY_BUCKET] Failed to verify bucket emptiness:`, verifyError);
        throw verifyError;
      }
      
      logger.info(`[EMPTY_BUCKET] Remaining files in bucket: ${remainingFiles?.length || 0}`);

      logger.info(`[EMPTY_BUCKET] Successfully emptied bucket '${bucketName}'`);
      return {
        data: { message: 'Successfully emptied' },
        error: null
      };
    } catch (error) {
      logger.error(`[EMPTY_BUCKET] Error emptying bucket '${bucketName}':`, error);
      throw error;
    }
  }

  public async downloadFile(bucketName: string, filePath: string): Promise<StorageResponse<Buffer>> {
    try {
      logger.info(`Downloading file from bucket '${bucketName}' at path '${filePath}'...`);
      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .download(filePath);

      if (error) {
        logger.error(`Error downloading file from '${filePath}':`, error);
        return { data: null, error };
      }

      if (!data) {
        const error = new Error('No data received from download');
        logger.error(`Error downloading file from '${filePath}':`, error);
        return { data: null, error };
      }

      let buffer: Buffer;
      if (data instanceof Buffer) {
        buffer = data;
      } else if (typeof data.arrayBuffer === 'function') {
        // Handle Blob/ArrayBuffer
        buffer = Buffer.from(await data.arrayBuffer());
      } else {
        const error = new Error('Unsupported data type received from download');
        logger.error(`Error downloading file from '${filePath}':`, error);
        return { data: null, error };
      }

      logger.info(`Successfully downloaded file from '${filePath}'`);
      return {
        data: buffer,
        error: null
      };
    } catch (error) {
      logger.error(`Error downloading file from '${filePath}':`, error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error during download')
      };
    }
  }

  public async listFiles(
    bucketName: string,
    folderPath: string = '',
    options: ListFilesOptions = {}
  ): Promise<ListFilesResponse> {
    try {
      logger.info(`Listing files in bucket '${bucketName}' at path '${folderPath}'...`);
      
      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .list(folderPath, {
          ...(options.limit && { limit: options.limit }),
          ...(options.offset && { offset: options.offset }),
          ...(options.sortBy && { sortBy: options.sortBy })
        });

      if (error) {
        logger.error(`Failed to list files in '${folderPath}':`, error);
        throw new Error(`Failed to list files: ${error.message}`);
      }

      logger.info(`Successfully listed files in '${folderPath}'`);
      return {
        data: data.map((file: any) => ({
          name: file.name,
          id: file.id,
          updated_at: file.updated_at,
          created_at: file.created_at,
          last_accessed_at: file.last_accessed_at,
          metadata: file.metadata
        })),
        error: null
      };
    } catch (error) {
      logger.error(`Error listing files in '${folderPath}':`, error);
      throw error;
    }
  }

  public async deleteFiles(
    bucketName: string,
    filePaths: string[]
  ): Promise<DeleteFilesResponse> {
    try {
      logger.info(`[DELETE_FILES] Starting deletion of ${filePaths.length} files from bucket '${bucketName}'`);
      logger.info(`[DELETE_FILES] Files to delete:`, filePaths);
      
      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .remove(filePaths);

      if (error) {
        logger.error(`[DELETE_FILES] Supabase storage.remove() error:`, error);
        throw new Error(`Failed to delete files: ${error.message}`);
      }

      logger.info(`[DELETE_FILES] Supabase storage.remove() response:`, {
        data,
        error,
        filePaths
      });

      // Verify deletion
      logger.info(`[DELETE_FILES] Verifying deletion...`);
      const { data: remainingFiles, error: listError } = await this.listFiles(bucketName);
      if (listError) {
        logger.error(`[DELETE_FILES] Failed to verify deletion:`, listError);
      } else {
        logger.info(`[DELETE_FILES] Remaining files after deletion:`, remainingFiles);
      }

      logger.info(`[DELETE_FILES] Successfully deleted files from bucket '${bucketName}'`);
      return { data, error: null };
    } catch (error) {
      logger.error(`[DELETE_FILES] Error deleting files from bucket '${bucketName}':`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const storageClient = StorageClient.getInstance(); 