import { logger } from '../utils/logger';
import { Bucket } from '@supabase/storage-js';
import { getServiceRoleClient } from '../config/supabase';

// Bucket name constants
export const BUCKETS = {
  FOOD_IMAGES: 'food-images',
  EVENT_IMAGES: 'event-images',
} as const;

export type BucketName = typeof BUCKETS[keyof typeof BUCKETS];

export interface BucketConfig {
  name: string;
  public: boolean;
  fileSizeLimit?: number;
  allowedMimeTypes?: string[];
}

export interface BucketResponse {
  id: string;
  name: string;
  owner: string;
  public: boolean;
  file_size_limit: number | null;
  allowed_mime_types: string[] | null;
  created_at: string;
  updated_at: string;
}

function mapBucketToResponse(bucket: Bucket): BucketResponse {
  return {
    id: bucket.id,
    name: bucket.name,
    owner: bucket.owner,
    public: bucket.public,
    file_size_limit: bucket.file_size_limit ?? null,
    allowed_mime_types: bucket.allowed_mime_types ?? null,
    created_at: bucket.created_at,
    updated_at: bucket.updated_at
  };
}

export interface UploadOptions {
  cacheControl?: string;
  upsert?: boolean;
  contentType?: string;
}

export interface UploadResponse {
  path: string;
  fullPath: string;
}

export interface DownloadResponse {
  data: Buffer | null;
  error: any;
}

export interface FileObject {
  name: string;
  id: string;
  updated_at: string;
  created_at: string;
  last_accessed_at: string;
  metadata: Record<string, any>;
}

export interface ListFilesOptions {
  limit?: number;
  offset?: number;
  sortBy?: {
    column: 'name' | 'created_at' | 'updated_at' | 'last_accessed_at';
    order: 'asc' | 'desc';
  };
}

export interface ListFilesResponse {
  data: FileObject[] | null;
  error: any;
}

export interface DeleteFilesResponse {
  data: any[] | null;
  error: any;
}

interface StorageResponse<T> {
  data: T | null;
  error: Error | null;
}

export interface StorageFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
  destination?: string;
  filename?: string;
  path?: string;
}

export class StorageClient {
  private static instance: StorageClient;
  private supabase;
  private initialized = false;
  private readonly buckets: BucketConfig[] = [
    {
      name: 'food-images',
      public: false,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif']
    },
    {
      name: 'event-images',
      public: false,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif']
    }
  ];

  private constructor() {
    this.supabase = getServiceRoleClient();
  }

  public static getInstance(): StorageClient {
    if (!StorageClient.instance) {
      StorageClient.instance = new StorageClient();
    }
    return StorageClient.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.info('Storage service already initialized');
      return;
    }

    try {
      logger.info('Starting storage service initialization...');
      // Create buckets if they don't exist
      for (const bucket of this.buckets) {
        logger.info(`Initializing bucket: ${bucket.name}`);
        await this.createBucket(bucket);
      }
      this.initialized = true;
      logger.info('Storage service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize storage service:', error);
      throw error;
    }
  }

  public async createBucket(config: BucketConfig): Promise<{ data: BucketResponse | null; error: any }> {
    try {
      logger.info(`Checking if bucket '${config.name}' exists...`);
      
      // List existing buckets using Supabase API
   //   const { data: existingBuckets, error: listError } = await this.supabase.storage.listBuckets();

   //   if (listError) {
   //     logger.error(`Failed to list buckets:`, listError);
    //    logger.error('Error details:', {
     //     message: listError.message,
     //     name: listError.name
     //   });
     //   return { data: null, error: listError };
     // }

     const existingBuckets = [];

      if (!existingBuckets.some((b: Bucket) => b.name === config.name)) {
        logger.info(`Creating bucket '${config.name}'...`);
        
        const { data, error } = await this.supabase.storage.createBucket(config.name, {
          public: "false",
          fileSizeLimit: config.fileSizeLimit ?? null,
          allowedMimeTypes: config.allowedMimeTypes ?? null
        });

        if (error) {
          logger.error(`Failed to create bucket '${config.name}':`, error);
          return { data: null, error };
        }

        logger.info(`Successfully created bucket: ${config.name}`);
        return { 
          data: mapBucketToResponse(data as Bucket),
          error: null 
        };
      } else {
        logger.info(`Bucket '${config.name}' already exists`);
        const bucket = existingBuckets.find((b: Bucket) => b.name === config.name);
        if (bucket) {
          return { 
            data: mapBucketToResponse(bucket as Bucket),
            error: null 
          };
        }
        return { data: null, error: null };
      }
    } catch (error) {
      logger.error(`Error managing bucket '${config.name}':`, error);
      return { data: null, error };
    }
  }

  private async validateBucket(bucketName: string): Promise<void> {
    const { data, error } = await this.getBucket(bucketName);
    if (error || !data) {
      throw new Error(`Bucket '${bucketName}' does not exist or is not accessible`);
    }
  }

  public async uploadFile(
    bucketName: string,
    filePath: string,
    file: Buffer,
    options: UploadOptions = {}
  ): Promise<{ data: UploadResponse | null; error: any }> {
    try {
      await this.validateBucket(bucketName);

      const { error } = await this.supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          ...(options.cacheControl && { cacheControl: options.cacheControl }),
          ...(options.upsert && { upsert: options.upsert }),
          ...(options.contentType && { contentType: options.contentType })
        });

      if (error) {
        logger.error(`Failed to upload file to '${filePath}' in bucket '${bucketName}':`, error);
        return { data: null, error };
      }

      return {
        data: {
          path: filePath,
          fullPath: `${bucketName}/${filePath}`
        },
        error: null
      };
    } catch (error) {
      logger.error(`Error uploading file to '${filePath}' in bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public async uploadImage(file: StorageFile, bucketName: string): Promise<string> {
    try {
      await this.validateBucket(bucketName);

      const filePath = `${file.fieldname}/${file.filename}`;
      const { error } = await this.supabase.storage
        .from(bucketName)
        .upload(filePath, file.buffer, {
          contentType: file.mimetype,
          upsert: true
        });

      if (error) {
        logger.error(`Failed to upload image to '${filePath}' in bucket '${bucketName}':`, error);
        throw error;
      }

      return this.getPublicUrl(bucketName, filePath);
    } catch (error) {
      logger.error(`Error uploading image to bucket '${bucketName}':`, error);
      throw error;
    }
  }

  public async deleteImage(imageUrl: string, bucket: BucketName): Promise<void> {
    try {
      const path = imageUrl.split('/').pop();
      if (!path) {
        throw new Error('Invalid image URL');
      }

      const { error } = await this.supabase.storage
        .from(bucket)
        .remove([path]);

      if (error) {
        logger.error(`Failed to delete image '${path}' from bucket '${bucket}':`, error);
        throw error;
      }
    } catch (error) {
      logger.error(`Error deleting image from bucket '${bucket}':`, error);
      throw error;
    }
  }

  public async getImage(imageUrl: string, bucket: BucketName): Promise<string> {
    try {
      const path = imageUrl.split('/').pop();
      if (!path) {
        throw new Error('Invalid image URL');
      }

      const { data, error } = await this.supabase.storage
        .from(bucket)
        .createSignedUrl(path, 3600);

      if (error) {
        logger.error(`Failed to get signed URL for image '${path}' from bucket '${bucket}':`, error);
        throw error;
      }

      return data.signedUrl;
    } catch (error) {
      logger.error(`Error getting image from bucket '${bucket}':`, error);
      throw error;
    }
  }

  public getPublicUrl(bucket: string, path: string): string {
    const { data } = this.supabase.storage
      .from(bucket)
      .getPublicUrl(path);
    return data.publicUrl;
  }

  public async getBucket(bucketName: string): Promise<{ data: BucketResponse | null; error: any }> {
    try {
      const { data, error } = await this.supabase.storage.getBucket(bucketName);
      if (error) {
        logger.error(`Failed to get bucket '${bucketName}':`, error);
        return { data: null, error };
      }
      return { data: mapBucketToResponse(data as Bucket), error: null };
    } catch (error) {
      logger.error(`Error getting bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public async listBuckets(): Promise<{ data: BucketResponse[] | null; error: any }> {
    try {
      const { data, error } = await this.supabase.storage.listBuckets();
      if (error) {
        logger.error('Failed to list buckets:', error);
        return { data: null, error };
      }
      return { 
        data: data.map(bucket => mapBucketToResponse(bucket as Bucket)), 
        error: null 
      };
    } catch (error) {
      logger.error('Error listing buckets:', error);
      return { data: null, error };
    }
  }

  public async deleteBucket(bucketName: string): Promise<{ data: { message: string } | null; error: any }> {
    try {
      await this.validateBucket(bucketName);
      await this.emptyBucket(bucketName);

      const { error } = await this.supabase.storage.deleteBucket(bucketName);
      if (error) {
        logger.error(`Failed to delete bucket '${bucketName}':`, error);
        return { data: null, error };
      }

      return { 
        data: { message: `Successfully deleted bucket '${bucketName}'` }, 
        error: null 
      };
    } catch (error) {
      logger.error(`Error deleting bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public async emptyBucket(bucketName: string): Promise<{ data: { message: string } | null; error: any }> {
    try {
      logger.info(`[EMPTY_BUCKET] Starting to empty bucket '${bucketName}'...`);
      await this.validateBucket(bucketName);

      logger.info(`[EMPTY_BUCKET] Listing files in bucket '${bucketName}'...`);
      const { data: files, error: listError } = await this.listFiles(bucketName);
      if (listError) {
        logger.error(`[EMPTY_BUCKET] Error listing files in bucket '${bucketName}':`, listError);
        return { data: null, error: listError };
      }

      if (files && files.length > 0) {
        const filePaths = files.map(file => file.name);
        logger.info(`[EMPTY_BUCKET] Deleting ${filePaths.length} files from bucket '${bucketName}'...`);
        const { error: deleteError } = await this.deleteFiles(bucketName, filePaths);
        if (deleteError) {
          logger.error(`[EMPTY_BUCKET] Error deleting files from bucket '${bucketName}':`, deleteError);
          return { data: null, error: deleteError };
        }
      }

      logger.info(`[EMPTY_BUCKET] Successfully emptied bucket '${bucketName}'`);
      return { 
        data: { message: `Successfully emptied bucket '${bucketName}'` }, 
        error: null 
      };
    } catch (error) {
      logger.error(`[EMPTY_BUCKET] Error emptying bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public async downloadFile(bucketName: string, filePath: string): Promise<StorageResponse<Buffer>> {
    try {
      await this.validateBucket(bucketName);

      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .download(filePath);

      if (error) {
        logger.error(`Failed to download file '${filePath}' from bucket '${bucketName}':`, error);
        return { data: null, error };
      }

      if (!data) {
        return { data: null, error: new Error('No data received from download') };
      }

      let buffer: Buffer;
      if (data instanceof Buffer) {
        buffer = data;
      } else if (typeof data.arrayBuffer === 'function') {
        // Handle Blob/ArrayBuffer
        buffer = Buffer.from(await data.arrayBuffer());
      } else {
        return { data: null, error: new Error('Unsupported data type received from download') };
      }

      return { data: buffer, error: null };
    } catch (error) {
      logger.error(`Error downloading file '${filePath}' from bucket '${bucketName}':`, error);
      return { data: null, error: error instanceof Error ? error : new Error('Unknown error during download') };
    }
  }

  public async listFiles(
    bucketName: string,
    folderPath: string = '',
    options: ListFilesOptions = {}
  ): Promise<ListFilesResponse> {
    try {
      logger.info(`Listing files in bucket '${bucketName}' at path '${folderPath}'...`);
      await this.validateBucket(bucketName);

      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .list(folderPath, {
          ...(options.limit && { limit: options.limit }),
          ...(options.offset && { offset: options.offset }),
          ...(options.sortBy && { sortBy: options.sortBy })
        });

      if (error) {
        logger.error(`Failed to list files in '${folderPath}':`, error);
        return { data: null, error };
      }

      const files = data.map(file => ({
        name: file.name,
        id: file.id,
        updated_at: file.updated_at,
        created_at: file.created_at,
        last_accessed_at: file.last_accessed_at,
        metadata: file.metadata
      }));

      return { data: files, error: null };
    } catch (error) {
      logger.error(`Error listing files in '${folderPath}':`, error);
      return { data: null, error };
    }
  }

  public async deleteFiles(
    bucketName: string,
    filePaths: string[]
  ): Promise<DeleteFilesResponse> {
    try {
      await this.validateBucket(bucketName);

      const { data, error } = await this.supabase.storage
        .from(bucketName)
        .remove(filePaths);

      if (error) {
        logger.error(`Failed to delete files from bucket '${bucketName}':`, error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      logger.error(`Error deleting files from bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }
}

export const storageClient = StorageClient.getInstance(); 