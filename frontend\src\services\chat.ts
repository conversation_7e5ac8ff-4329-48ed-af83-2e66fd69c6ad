import { apiService } from './api';

export interface ChatGroup {
  id: string;
  name: string;
  createdAt: Date;
  createdBy: string;
  members: string[];
  iconUrl: string | null;
  dataAiHint: string | null;
  lastMessage: string | null;
  lastMessageTimestamp: Date | null;
}

export interface ChatMessage {
  id: string;
  groupId: string;
  senderId: string;
  senderName: string;
  text: string;
  timestamp: Date;
  metadata?: any;
}

class ChatService {
  async getChatGroups(): Promise<ChatGroup[]> {
    try {
      const response = await apiService.getChatGroups();
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        return [];
      }
      return response.data.map((group: any) => ({
        ...group,
        createdAt: new Date(group.createdAt),
        lastMessageTimestamp: group.lastMessageTimestamp ? new Date(group.lastMessageTimestamp) : null,
      }));
    } catch (error) {
      console.error('Error fetching chat groups:', error);
      throw error;
    }
  }

  async getChatGroup(groupId: string): Promise<ChatGroup> {
    try {
      console.log('🔍 ChatService: Fetching group info for ID:', groupId);
      const response = await apiService.getChatGroup(groupId);

      console.log('📨 ChatService: Group API response:', response);

      if (response.error) {
        console.error('❌ ChatService: API returned error:', response.error);
        throw new Error(response.error);
      }
      if (!response.data) {
        console.error('❌ ChatService: No group data in response');
        throw new Error('Chat group not found');
      }

      const group = response.data;
      console.log('📊 ChatService: Group data received:', {
        id: group.id,
        name: group.name,
        memberCount: group.members?.length || 0,
        members: group.members
      });

      const result = {
        ...group,
        createdAt: new Date(group.createdAt),
        lastMessageTimestamp: group.lastMessageTimestamp ? new Date(group.lastMessageTimestamp) : null,
      };

      console.log('✅ ChatService: Returning formatted group:', {
        id: result.id,
        name: result.name,
        memberCount: result.members?.length || 0
      });

      return result;
    } catch (error) {
      console.error('💥 ChatService: Error fetching chat group:', error);
      throw error;
    }
  }

  async getChatMessages(groupId: string): Promise<ChatMessage[]> {
    try {
      const response = await apiService.getChatMessages(groupId);
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        return [];
      }
      return response.data.map((message: any) => ({
        ...message,
        timestamp: new Date(message.timestamp),
      }));
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      throw error;
    }
  }

  async sendMessage(groupId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>): Promise<ChatMessage> {
    try {
      console.log('🚀 ChatService: Sending message to group:', groupId);
      console.log('📝 ChatService: Message data:', message);

      // Convert camelCase to snake_case for backend
      const backendMessage = {
        group_id: groupId,
        community_id: 'default',
        sender_id: message.senderId,
        sender_name: message.senderName,
        text: message.text,
        timestamp: new Date().toISOString(),
      };

      console.log('📤 ChatService: Sending to backend:', backendMessage);

      const response = await apiService.sendMessage(groupId, backendMessage);

      console.log('📨 ChatService: Response received:', response);

      if (response.error) {
        console.error('❌ ChatService: API returned error:', response.error);
        throw new Error(response.error);
      }
      if (!response.data) {
        console.error('❌ ChatService: No data in response');
        throw new Error('Failed to send message');
      }

      console.log('✅ ChatService: Message sent successfully');

      return {
        ...response.data,
        timestamp: new Date(response.data.timestamp),
      };
    } catch (error) {
      console.error('💥 ChatService: Error sending message:', error);
      throw error;
    }
  }

  async createChatGroup(groupData: Omit<ChatGroup, 'id' | 'createdAt'>): Promise<ChatGroup> {
    try {
      console.log('🚀 ChatService: Starting group creation request');
      console.log('📋 ChatService: Group data:', {
        name: groupData.name,
        memberCount: groupData.members?.length || 0,
        createdBy: groupData.createdBy
      });

      const response = await apiService.createChatGroup(groupData);

      console.log('📨 ChatService: API response received');
      console.log('📊 ChatService: Response status:', response.error ? 'ERROR' : 'SUCCESS');

      if (response.error) {
        console.error('❌ ChatService: API returned error:', response.error);
        throw new Error(response.error);
      }

      if (!response.data) {
        console.error('❌ ChatService: No data in response');
        throw new Error('Failed to create chat group - no data returned');
      }

      console.log('✅ ChatService: Group created successfully');
      console.log('🆔 ChatService: New group ID:', response.data.id);

      const result = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        lastMessageTimestamp: response.data.lastMessageTimestamp ? new Date(response.data.lastMessageTimestamp) : null,
      };

      console.log('🎯 ChatService: Returning formatted group data');
      return result;
    } catch (error: any) {
      console.error('💥 ChatService: Error in createChatGroup:', error);
      console.error('🔍 ChatService: Error details:', {
        message: error?.message,
        name: error?.name,
        stack: error?.stack?.split('\n')[0] // Just first line of stack
      });
      throw error;
    }
  }
}

export const chatService = new ChatService();
