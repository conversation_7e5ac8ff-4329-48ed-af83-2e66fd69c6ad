import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { createCardStyle, getSpacing } from '../../theme';

interface StyledCardProps {
  children: React.ReactNode;
  elevated?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}

const StyledCard: React.FC<StyledCardProps> = ({
  children,
  elevated = true,
  padding = 'lg',
  style,
}) => {
  const cardStyle = createCardStyle(elevated);
  
  const paddingStyle = {
    padding: padding === 'none' ? 0 : getSpacing(padding as any),
  };

  return (
    <View style={[cardStyle, paddingStyle, style]}>
      {children}
    </View>
  );
};

export default StyledCard;
