import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { SurveyQuestion, CreateSurveyData, surveysService } from '@services/surveys';
import { useAuth } from '@contexts/AuthContext';

type CreateSurveyScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'CreateSurvey'>;

export default function CreateSurveyScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateSurveyScreenNavigationProp>();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    closingDate: '',
  });

  const [questions, setQuestions] = useState<SurveyQuestion[]>([
    {
      id: '1',
      text: '',
      type: 'single-choice' as const,
      options: [''],
      required: true,
    }
  ]);

  const handleSubmit = async () => {
    if (!formData.title || !formData.description) {
      Alert.alert('שגיאה', 'אנא מלא את כל השדות החובה');
      return;
    }

    // Validate questions
    const validQuestions = questions.filter(q => q.text.trim() !== '');
    if (validQuestions.length === 0) {
      Alert.alert('שגיאה', 'אנא הוסף לפחות שאלה אחת');
      return;
    }

    setLoading(true);
    try {
      const surveyData: CreateSurveyData = {
        title: formData.title,
        description: formData.description,
        questions: validQuestions,
        status: 'OPEN',
        closingDate: formData.closingDate ? new Date(formData.closingDate) : undefined,
      };

      const response = await surveysService.createSurvey(surveyData);
      if (response) {
        Alert.alert('הצלחה', 'הסקר נוצר בהצלחה', [
          { text: 'אישור', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Error creating survey:', error);
      Alert.alert('שגיאה', 'אירעה שגיאה ביצירת הסקר');
    } finally {
      setLoading(false);
    }
  };

  const addQuestion = () => {
    const newQuestion: SurveyQuestion = {
      id: Date.now().toString(),
      text: '',
      type: 'single-choice',
      options: [''],
      required: false,
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (index: number, field: keyof SurveyQuestion, value: any) => {
    const updatedQuestions = [...questions];
    updatedQuestions[index] = { ...updatedQuestions[index], [field]: value };
    setQuestions(updatedQuestions);
  };

  const addOption = (questionIndex: number) => {
    const updatedQuestions = [...questions];
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options!.push('');
      setQuestions(updatedQuestions);
    }
  };

  const updateOption = (questionIndex: number, optionIndex: number, value: string) => {
    const updatedQuestions = [...questions];
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options![optionIndex] = value;
      setQuestions(updatedQuestions);
    }
  };

  const removeQuestion = (index: number) => {
    if (questions.length > 1) {
      const updatedQuestions = questions.filter((_, i) => i !== index);
      setQuestions(updatedQuestions);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="📝 יצירת סקר"
        showBackButton={true}
        rightComponent={
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.saveButtonText}>שמור</Text>
          </TouchableOpacity>
        }
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={[styles.label, { textAlign: getTextAlign() }]}>כותרת הסקר</Text>
          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="לדוגמה: מה חשוב לכם במרכז הקהילתי?"
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
          />

          <Text style={[styles.label, { textAlign: getTextAlign() }]}>תיאור</Text>
          <TextInput
            style={[styles.textArea, { textAlign: getTextAlign() }]}
            placeholder="אנא שתפו מה חשוב לכם שיהיה בפרויקט החדש"
            value={formData.description}
            onChangeText={(text) => setFormData({ ...formData, description: text })}
            multiline
            numberOfLines={3}
          />

          <Text style={[styles.label, { textAlign: getTextAlign() }]}>תאריך סיום</Text>
          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="YYYY-MM-DD"
            value={formData.closingDate}
            onChangeText={(text) => setFormData({ ...formData, closingDate: text })}
          />
        </View>

        {/* Questions Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { textAlign: getTextAlign() }]}>שאלות</Text>

          {questions.map((question, questionIndex) => (
            <View key={question.id} style={styles.questionContainer}>
              <View style={styles.questionHeader}>
                <Text style={styles.questionNumber}>שאלה {questionIndex + 1}</Text>
                {questions.length > 1 && (
                  <TouchableOpacity
                    onPress={() => removeQuestion(questionIndex)}
                    style={styles.removeButton}
                  >
                    <Icon name="close" iconSet="MaterialIcons" size={20} color={getColor('error')} />
                  </TouchableOpacity>
                )}
              </View>

              <TextInput
                style={[styles.input, { textAlign: getTextAlign() }]}
                placeholder={`שאלה ${questionIndex + 1}: מה הכי חשוב לכם?`}
                value={question.text}
                onChangeText={(text) => updateQuestion(questionIndex, 'text', text)}
              />

              {/* Question Options */}
              {question.type !== 'text' && question.options && (
                <View style={styles.optionsContainer}>
                  {question.options.map((option, optionIndex) => (
                    <View key={optionIndex} style={styles.optionRow}>
                      <TextInput
                        style={[styles.optionInput, { textAlign: getTextAlign() }]}
                        placeholder={`תשובה אפשרית ${optionIndex + 1}`}
                        value={option}
                        onChangeText={(text) => updateOption(questionIndex, optionIndex, text)}
                      />
                      {optionIndex === question.options!.length - 1 && (
                        <TouchableOpacity
                          onPress={() => addOption(questionIndex)}
                          style={styles.addOptionButton}
                        >
                          <Text style={styles.addOptionText}>➕</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}

          <TouchableOpacity
            style={styles.addQuestionButton}
            onPress={addQuestion}
          >
            <Text style={[styles.addQuestionText, { textAlign: getTextAlign() }]}>
              ➕ הוסף שאלה נוספת
            </Text>
          </TouchableOpacity>
        </View>

        {/* Create Button */}
        <TouchableOpacity
          style={styles.createButton}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={[styles.createButtonText, { textAlign: getTextAlign() }]}>
            {loading ? 'יוצר סקר...' : 'צור סקר'}
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle({
    flex: 1,
    backgroundColor: getColor('background'),
  }),
  content: {
    flex: 1,
  },
  scrollContent: createWebCompatibleScrollContentStyle({
    flexGrow: 1,
    padding: getSpacing('md'),
  }),
  section: {
    marginBottom: getSpacing('xl'),
  },
  sectionTitle: {
    ...getTypography('headingMedium'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('md'),
  },
  label: {
    ...getTypography('labelMedium'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('xs'),
    marginTop: getSpacing('sm'),
  },
  input: {
    backgroundColor: getColor('surface'),
    borderWidth: 1,
    borderColor: getColor('outline'),
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('sm'),
  },
  textArea: {
    backgroundColor: getColor('surface'),
    borderWidth: 1,
    borderColor: getColor('outline'),
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('sm'),
    minHeight: 80,
    textAlignVertical: 'top',
  },
  questionContainer: {
    backgroundColor: getColor('surface'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    marginBottom: getSpacing('md'),
    ...getShadow('xs'),
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getSpacing('sm'),
  },
  questionNumber: {
    ...getTypography('labelMedium'),
    color: getColor('primary'),
    fontWeight: 'bold',
  },
  removeButton: {
    padding: getSpacing('xs'),
  },
  optionsContainer: {
    marginTop: getSpacing('sm'),
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('xs'),
  },
  optionInput: {
    flex: 1,
    backgroundColor: getColor('background'),
    borderWidth: 1,
    borderColor: getColor('outline'),
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('sm'),
    ...getTypography('bodySmall'),
    color: getColor('onSurface'),
    marginRight: getSpacing('sm'),
  },
  addOptionButton: {
    backgroundColor: getColor('surface'),
    borderWidth: 1,
    borderColor: getColor('outline'),
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('sm'),
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addOptionText: {
    fontSize: 16,
  },
  addQuestionButton: {
    backgroundColor: getColor('surface'),
    borderWidth: 1,
    borderColor: getColor('outline'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    alignItems: 'center',
    marginTop: getSpacing('md'),
  },
  addQuestionText: {
    ...getTypography('labelMedium'),
    color: getColor('onSurface'),
  },
  createButton: {
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    alignItems: 'center',
    marginTop: getSpacing('xl'),
    ...getShadow('sm'),
  },
  createButtonText: {
    ...getTypography('labelLarge'),
    color: '#fff',
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: getBorderRadius('sm'),
    paddingHorizontal: getSpacing('md'),
    paddingVertical: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonText: {
    ...getTypography('labelMedium'),
    color: '#fff',
    fontWeight: 'bold',
  },
});
