import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { SurveyQuestion, CreateSurveyData, surveysService } from '@services/surveys';
import { useAuth } from '@contexts/AuthContext';

type CreateSurveyScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'CreateSurvey'>;

export default function CreateSurveyScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateSurveyScreenNavigationProp>();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    closingDate: '',
    imageUrl: '',
    questions: [
      {
        id: '1',
        text: '',
        type: 'single-choice' as const,
        options: [''],
        required: true,
      }
    ],
  });

  const handleSubmit = async () => {
    if (!formData.title || !formData.description || formData.questions.length === 0) {
      console.log('❌ Survey validation failed:', {
        hasTitle: !!formData.title,
        hasDescription: !!formData.description,
        questionsCount: formData.questions.length
      });
      Alert.alert('שגיאה', 'אנא מלא את כל השדות החובה');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Starting survey creation:', {
        title: formData.title,
        description: formData.description,
        questionsCount: formData.questions.length,
        questions: formData.questions.map(q => ({
          text: q.text,
          type: q.type,
          optionsCount: q.options?.length
        }))
      });

      const surveyData: CreateSurveyData = {
        title: formData.title,
        description: formData.description,
        questions: formData.questions,
        openDate: new Date(),
        closeDate: formData.closingDate ? new Date(formData.closingDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Default to 7 days from now
        isAnonymous: false,
        imageUrl: formData.imageUrl || undefined,
      };

      console.log('📝 Submitting survey data:', {
        ...surveyData,
        openDate: surveyData.openDate.toISOString(),
        closeDate: surveyData.closeDate.toISOString()
      });

      const response = await surveysService.createSurvey(surveyData);
      
      if (response.data) {
        console.log('✅ Survey created successfully:', {
          id: response.data.id,
          title: response.data.title
        });
        navigation.goBack();
      } else {
        console.error('❌ Failed to create survey:', response.error);
        Alert.alert('שגיאה', 'שגיאה ביצירת הסקר');
      }
    } catch (error) {
      console.error('❌ Error creating survey:', error);
      Alert.alert('שגיאה', 'שגיאה ביצירת הסקר');
    } finally {
      setLoading(false);
    }
  };

  const addQuestion = () => {
    console.log('➕ Adding new question');
    const newQuestion: SurveyQuestion = {
      id: Date.now().toString(),
      text: '',
      type: 'single-choice',
      options: [''],
      required: true,
    };
    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
  };

  const removeQuestion = (index: number) => {
    console.log('➖ Removing question at index:', index);
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
  };

  const updateQuestion = (index: number, field: keyof SurveyQuestion, value: any) => {
    console.log('📝 Updating question:', {
      index,
      field,
      value
    });
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === index ? { ...q, [field]: value } : q
      )
    }));
  };

  const addOption = (questionIndex: number) => {
    const updatedQuestions = [...formData.questions];
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options!.push('');
      setFormData(prev => ({
        ...prev,
        questions: updatedQuestions
      }));
    }
  };

  const updateOption = (questionIndex: number, optionIndex: number, value: string) => {
    const updatedQuestions = [...formData.questions];
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options![optionIndex] = value;
      setFormData(prev => ({
        ...prev,
        questions: updatedQuestions
      }));
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="📝 יצירת סקר"
        showBackButton={true}
        rightComponent={
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.saveButtonText}>
              {loading ? 'שומר...' : 'שמור'}
            </Text>
          </TouchableOpacity>
        }
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Basic Information */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>כותרת הסקר</Text>
            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder="לדוגמה: מה חשוב לכם במרכז הקהילתי?"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
              placeholderTextColor={getColor('onSurface', 400)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>תיאור</Text>
            <TextInput
              style={[styles.textArea, { textAlign: getTextAlign() }]}
              placeholder="אנא שתפו מה חשוב לכם שיהיה בפרויקט החדש"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
              placeholderTextColor={getColor('onSurface', 400)}
            />
          </View>
        </View>

        {/* Questions Section */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>שאלות</Text>

            {formData.questions.map((question, questionIndex) => (
              <View key={question.id} style={styles.questionCard}>
                <View style={styles.questionHeader}>
                  <Text style={styles.questionNumber}>שאלה {questionIndex + 1}</Text>
                  {formData.questions.length > 1 && (
                    <TouchableOpacity
                      onPress={() => removeQuestion(questionIndex)}
                      style={styles.removeButton}
                    >
                      <Icon name="close" iconSet="MaterialIcons" size={18} color={getColor('error')} />
                    </TouchableOpacity>
                  )}
                </View>

                <TextInput
                  style={[styles.questionInput, { textAlign: getTextAlign() }]}
                  placeholder={`שאלה ${questionIndex + 1}: מה הכי חשוב לכם?`}
                  value={question.text}
                  onChangeText={(text) => updateQuestion(questionIndex, 'text', text)}
                  placeholderTextColor={getColor('onSurface', 400)}
                />

                {/* Question Options */}
                {question.type !== 'text' && question.options && (
                  <View style={styles.optionsContainer}>
                    {question.options.map((option, optionIndex) => (
                      <View key={optionIndex} style={styles.optionRow}>
                        <TextInput
                          style={[styles.optionInput, { textAlign: getTextAlign() }]}
                          placeholder={`תשובה אפשרית ${optionIndex + 1}`}
                          value={option}
                          onChangeText={(text) => updateOption(questionIndex, optionIndex, text)}
                          placeholderTextColor={getColor('onSurface', 400)}
                        />
                        {optionIndex === question.options!.length - 1 && (
                          <TouchableOpacity
                            onPress={() => addOption(questionIndex)}
                            style={styles.addOptionButton}
                          >
                            <Text style={styles.addOptionText}>➕</Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  </View>
                )}
              </View>
            ))}

            <TouchableOpacity
              style={styles.addQuestionButton}
              onPress={addQuestion}
            >
              <Text style={[styles.addQuestionText, { textAlign: getTextAlign() }]}>
                ➕ הוסף שאלה נוספת
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Additional Settings */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>תאריך סיום</Text>
            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder="YYYY-MM-DD (אופציונלי)"
              value={formData.closingDate}
              onChangeText={(text) => setFormData({ ...formData, closingDate: text })}
              placeholderTextColor={getColor('onSurface', 400)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { textAlign: getTextAlign() }]}>תמונה (אופציונלי)</Text>
            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder="קישור לתמונה"
              value={formData.imageUrl}
              onChangeText={(text) => setFormData({ ...formData, imageUrl: text })}
              placeholderTextColor={getColor('onSurface', 400)}
            />
          </View>
        </View>

        {/* Create Button */}
        <TouchableOpacity
          style={[styles.createButton, loading && styles.createButtonDisabled]}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={[styles.createButtonText, { textAlign: getTextAlign() }]}>
            {loading ? 'יוצר סקר...' : 'צור סקר'}
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle({
    flex: 1,
    backgroundColor: getColor('background'),
  }),
  content: {
    flex: 1,
  },
  scrollContent: createWebCompatibleScrollContentStyle({
    flexGrow: 1,
    padding: getSpacing('lg'),
  }),
  formSection: {
    marginBottom: getSpacing('xl'),
  },
  inputGroup: {
    marginBottom: getSpacing('lg'),
  },
  label: {
    ...getTypography('labelLarge'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('sm'),
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: getColor('outline', 300),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    fontSize: 16,
    ...getShadow('xs'),
  },
  textArea: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: getColor('outline', 300),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    minHeight: 80,
    textAlignVertical: 'top',
    fontSize: 16,
    ...getShadow('xs'),
  },
  questionCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('md'),
    ...getShadow('sm'),
  },
  questionInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: getColor('outline', 300),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    fontSize: 16,
    marginBottom: getSpacing('sm'),
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getSpacing('md'),
  },
  questionNumber: {
    ...getTypography('labelLarge'),
    color: getColor('primary'),
    fontWeight: 'bold',
  },
  removeButton: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('xs'),
  },
  optionsContainer: {
    marginTop: getSpacing('sm'),
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('xs'),
  },
  optionInput: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: getColor('outline', 300),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    marginRight: getSpacing('sm'),
    fontSize: 14,
  },
  addOptionButton: {
    backgroundColor: '#ecf0f1',
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('sm'),
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addOptionText: {
    fontSize: 18,
  },
  addQuestionButton: {
    backgroundColor: '#ecf0f1',
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    marginTop: getSpacing('lg'),
    ...getShadow('xs'),
  },
  addQuestionText: {
    ...getTypography('labelLarge'),
    color: '#2c3e50',
    fontWeight: '600',
  },
  createButton: {
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    marginTop: getSpacing('xl'),
    marginBottom: getSpacing('xl'),
    ...getShadow('md'),
  },
  createButtonDisabled: {
    backgroundColor: getColor('neutral', 400),
    opacity: 0.7,
  },
  createButtonText: {
    ...getTypography('headingSmall'),
    color: '#fff',
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: getBorderRadius('md'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('md'),
    justifyContent: 'center',
    alignItems: 'center',
    ...getShadow('xs'),
  },
  saveButtonText: {
    ...getTypography('labelLarge'),
    color: '#fff',
    fontWeight: 'bold',
  },
});
