import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  TextInput,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { Job } from '../../types';
import { apiService } from '../../services/api';
import { JobsStackParamList } from '../../navigation/types';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';

type JobsListScreenNavigationProp = StackNavigationProp<JobsStackParamList, 'JobsList'>;

export default function JobsListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<JobsListScreenNavigationProp>();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadJobs();
  }, []);

  const loadJobs = async () => {
    try {
      console.log('🔄 Loading jobs...');
      const response = await apiService.getJobs();
      if (response.data) {
        setJobs(response.data);
        console.log('✅ Jobs loaded successfully:', response.data.length);
      } else {
        console.error('❌ Error loading jobs:', response.error);
      }
    } catch (error) {
      console.error('❌ Error loading jobs:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadJobs();
  };

  const handleAddJob = () => {
    console.log('🎯 Add job button clicked');
    try {
      navigation.navigate('CreateJob');
      console.log('✅ Navigation to CreateJob successful');
    } catch (error) {
      console.error('❌ Navigation to CreateJob failed:', error);
      alert('יצירת משרה חדשה - בקרוב!');
    }
  };

  const handleJobPress = (jobId: string) => {
    console.log('🎯 Job card clicked, jobId:', jobId);
    try {
      navigation.navigate('JobDetails', { jobId });
      console.log('✅ Navigation to JobDetails successful');
    } catch (error) {
      console.error('❌ Navigation to JobDetails failed:', error);
      alert(`פרטי משרה: ${jobId}`);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'לפני פחות משעה';
    } else if (diffInHours < 24) {
      return `לפני ${diffInHours} שעות`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `לפני ${diffInDays} ימים`;
    }
  };

  const filteredJobs = jobs.filter(job =>
    job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    job.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderJobItem = ({ item }: { item: Job }) => {
    const timeAgo = getTimeAgo(item.createdAt);
    const jobIcon = (item as any).icon || '💼'; // Use icon from job data or default

    return (
      <TouchableOpacity
        style={styles.jobCard}
        onPress={() => handleJobPress(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.cardIcon}>
          <Text style={styles.cardIconText}>{jobIcon}</Text>
        </View>

        <View style={styles.cardText}>
          <Text style={styles.jobTitle}>{item.title}</Text>
          <Text style={styles.jobDescription}>{item.description}</Text>
        </View>

        <View style={styles.cardMeta}>
          <Text style={styles.priceText}>{item.dateTimeInfo}</Text>
          <Text style={styles.timeText}>{timeAgo}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען עבודות...</Text>
      </View>
    );
  }

  const addButton = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleAddJob}
    >
      <Text style={styles.addButtonText}>פרסם</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Header
        title="💼 משרות מקומיות"
        showBackButton={true}
        rightComponent={addButton}
      />

      {/* Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <TextInput
          style={styles.searchBar}
          placeholder="🔍 חפש משרות..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />

        {/* Jobs List */}
        <FlatList
          data={filteredJobs}
          renderItem={renderJobItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* FAB */}
      <TouchableOpacity style={styles.fab} onPress={handleAddJob}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchBar: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    textAlign: 'right',
  },
  listContainer: {
    paddingBottom: 100,
  },
  jobCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardIcon: {
    backgroundColor: '#f0f8ff',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
    minWidth: 50,
    minHeight: 50,
  },
  cardIconText: {
    fontSize: 24,
  },
  cardText: {
    flex: 1,
    marginRight: 15,
  },
  jobTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'right',
  },
  jobDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    textAlign: 'right',
  },
  cardMeta: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    minHeight: 50,
  },
  priceText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'right',
  },
  timeText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
  },
  fab: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  fabText: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
});
