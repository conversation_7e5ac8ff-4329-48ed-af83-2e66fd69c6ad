import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import { ChatGroup } from '../../types/chat';
import { apiService } from '../../services/api';
import { ChatStackParamList } from '../../navigation/types';
import Header from '../../components/Header';

type ChatListScreenNavigationProp = StackNavigationProp<ChatStackParamList, 'ChatList'>;

const mockChats = [
  {
    id: '1',
    name: 'הודעות הכפר',
    lastMessage: 'לוח זמנים חדש למיחזור פורסם',
    timestamp: '9:15',
    unreadCount: 2,
    avatar: '🏘️',
    createdAt: new Date(),
    createdBy: 'admin',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
  {
    id: '2',
    name: 'מריה רודריגז',
    lastMessage: 'תודה על העזרה בגינה!',
    timestamp: '8:30',
    unreadCount: 0,
    avatar: 'M',
    createdAt: new Date(),
    createdBy: 'maria',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
  {
    id: '3',
    name: 'קבוצת הורים',
    lastMessage: 'שרה: אימון כדורגל עבר ל-16:00',
    timestamp: '7:45',
    unreadCount: 5,
    avatar: '👨‍👩‍👧‍👦',
    createdAt: new Date(),
    createdBy: 'parents',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
  {
    id: '4',
    name: 'יוחנן סמית',
    lastMessage: 'עבודת האומן עדיין זמינה',
    timestamp: 'אתמול',
    unreadCount: 0,
    avatar: 'J',
    createdAt: new Date(),
    createdBy: 'john',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
  {
    id: '5',
    name: 'עדכוני השוק המקומי',
    lastMessage: 'תותים טריים הגיעו היום!',
    timestamp: 'אתמול',
    unreadCount: 1,
    avatar: '🛒',
    createdAt: new Date(),
    createdBy: 'market',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
  {
    id: '6',
    name: 'אנה וילסון',
    lastMessage: 'מצאתי את החתול האבוד שלך ליד הפארק',
    timestamp: 'אתמול',
    unreadCount: 0,
    avatar: 'A',
    createdAt: new Date(),
    createdBy: 'anna',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
  {
    id: '7',
    name: 'תכנון אירועים',
    lastMessage: 'מייק: אנחנו צריכים עוד מתנדבים ל...',
    timestamp: 'לפני יומיים',
    unreadCount: 0,
    avatar: '🎪',
    createdAt: new Date(),
    createdBy: 'events',
    members: [],
    iconUrl: null,
    dataAiHint: null,
    lastMessageTimestamp: new Date(),
  },
];

export default function ChatListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<ChatListScreenNavigationProp>();
  const [chats, setChats] = useState<ChatGroup[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      console.log('🔄 Loading chats...');
      const response = await apiService.getChatGroups();
      if (response.data) {
        setChats(response.data);
        console.log('✅ Chats loaded successfully:', response.data.length);
      } else {
        console.error('❌ Error loading chats:', response.error);
      }
    } catch (error) {
      console.error('❌ Error loading chats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadChats();
  };

  const handleCreateGroup = () => {
    navigation.navigate('CreateGroup');
  };

  const handleChatPress = (groupId: string) => {
    navigation.navigate('ChatRoom', { groupId });
  };

  const getAvatarDisplay = (chat: any) => {
    if (chat.avatar) {
      return chat.avatar;
    }
    if (chat.iconUrl) {
      return chat.iconUrl;
    }
    // Generate initials from name
    return chat.name.split(' ').map((word: string) => word[0]).join('').substring(0, 2).toUpperCase();
  };

  const formatTime = (timestamp: string | Date) => {
    if (typeof timestamp === 'string') {
      return timestamp;
    }
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInHours = (now.getTime() - messageTime.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageTime.toLocaleTimeString('he-IL', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    } else if (diffInHours < 48) {
      return 'אתמול';
    } else {
      return `לפני ${Math.floor(diffInHours / 24)} ימים`;
    }
  };

  const filteredChats = chats.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (chat.lastMessage && chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const renderChatItem = ({ item }: { item: any }) => {
    const avatarDisplay = getAvatarDisplay(item);
    const timeDisplay = formatTime(item.lastMessageTimestamp || item.timestamp);

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => handleChatPress(item.id)}
      >
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>{avatarDisplay}</Text>
        </View>
        <View style={styles.chatContent}>
          <Text style={[styles.chatName, { textAlign: getTextAlign() }]}>
            {item.name}
          </Text>
          <Text style={[styles.chatPreview, { textAlign: getTextAlign() }]}>
            {item.lastMessage || 'אין הודעות'}
          </Text>
        </View>
        <View style={styles.chatMeta}>
          <Text style={styles.chatTime}>{timeDisplay}</Text>
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>{item.unreadCount}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען שיחות...</Text>
      </View>
    );
  }

  const addButton = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleCreateGroup}
    >
      <Text style={styles.addButtonText}>+ חדש</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Header
        title="💬 צ'אט קהילתי"
        showBackButton={true}
        rightComponent={addButton}
      />

      {/* Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <TextInput
          style={styles.searchBar}
          placeholder="🔍 חפש שיחות..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />

        {/* Chat List */}
        <FlatList
          data={filteredChats}
          renderItem={renderChatItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchBar: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    textAlign: 'right',
  },
  listContainer: {
    paddingBottom: 20,
  },
  chatItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#45b7d1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  chatContent: {
    flex: 1,
    marginRight: 10,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
    textAlign: 'right',
  },
  chatPreview: {
    fontSize: 14,
    color: '#7f8c8d',
    textAlign: 'right',
  },
  chatMeta: {
    alignItems: 'flex-end',
  },
  chatTime: {
    fontSize: 12,
    color: '#bdc3c7',
    marginBottom: 5,
  },
  unreadBadge: {
    backgroundColor: '#45b7d1',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
