import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { minioStorageClient, BUCKETS } from '../minio-storage.service';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('MinioStorageTest');

describe('MinIO Storage Service - Real Tests', () => {
  const TEST_BUCKET = BUCKETS.FOOD_IMAGES;
  const TEST_FILE_PATH = 'test-folder/test-file.txt';
  const TEST_FILE_CONTENT = 'Hello, this is a test file!';
  let testFileBuffer: Buffer;

  beforeAll(async () => {
    testFileBuffer = Buffer.from(TEST_FILE_CONTENT);
    // Initialize MinIO storage client
    await minioStorageClient.initialize();
  });

  afterAll(async () => {
    try {
      // Cleanup: Delete all files in the test bucket
      const { data: files } = await minioStorageClient.listFiles(TEST_BUCKET);
      if (files && files.length > 0) {
        await minioStorageClient.deleteFiles(TEST_BUCKET, files.map(f => f.name));
      }
      logger.info('Test cleanup completed');
    } catch (error) {
      logger.error('Error during test cleanup:', error);
    }
  });

  it('should perform a complete storage lifecycle', async () => {
    logger.info('Starting MinIO Storage Lifecycle Test');

    // 1. Create bucket
    logger.info('Step 1: Creating bucket...');
    const createResponse = await minioStorageClient.createBucket({
      name: TEST_BUCKET,
      public: false,
      fileSizeLimit: 1024 * 1024,
      allowedMimeTypes: ['text/plain']
    });
    expect(createResponse.error).toBeNull();
    expect(createResponse.data).toBeTruthy();
    logger.info('✓ Bucket created successfully');

    // 2. Upload file
    logger.info('Step 2: Uploading file...');
    const uploadResponse = await minioStorageClient.uploadFile(
      TEST_BUCKET,
      TEST_FILE_PATH,
      testFileBuffer,
      {
        contentType: 'text/plain'
      }
    );
    expect(uploadResponse.error).toBeNull();
    expect(uploadResponse.data?.path).toBe(TEST_FILE_PATH);
    logger.info('✓ File uploaded successfully');

    // 3. List files in bucket
    logger.info('Step 3: Listing files in bucket...');
    const listResponse = await minioStorageClient.listFiles(TEST_BUCKET, {
      prefix: 'test-folder/',
      recursive: true
    });
    expect(listResponse.error).toBeNull();
    expect(listResponse.data).toHaveLength(1);
    const firstFile = listResponse.data?.[0];
    expect(firstFile).toBeDefined();
    expect(firstFile?.name).toBe(TEST_FILE_PATH);
    logger.info('✓ Files listed successfully');

    // 4. Get file URL
    logger.info('Step 4: Getting file URL...');
    const fileUrl = minioStorageClient.getPublicUrl(TEST_BUCKET, TEST_FILE_PATH);
    expect(fileUrl).toBeTruthy();
    expect(fileUrl).toContain(TEST_BUCKET);
    expect(fileUrl).toContain(TEST_FILE_PATH);
    logger.info('✓ File URL generated successfully');

    // 5. Delete file
    logger.info('Step 5: Deleting file...');
    const deleteResponse = await minioStorageClient.deleteFiles(TEST_BUCKET, [TEST_FILE_PATH]);
    expect(deleteResponse.error).toBeNull();
    logger.info('✓ File deleted successfully');

    // 6. Verify bucket is empty
    logger.info('Step 6: Verifying bucket is empty...');
    const finalListResponse = await minioStorageClient.listFiles(TEST_BUCKET);
    expect(finalListResponse.error).toBeNull();
    expect(finalListResponse.data).toHaveLength(0);
    logger.info('✓ Bucket is empty');

    logger.info('✓ All MinIO storage operations completed successfully');
  });

  it('should handle image upload and retrieval', async () => {
    logger.info('Starting Image Upload Test');

    const testImage: any = {
      fieldname: 'image',
      originalname: 'test-image.jpg',
      encoding: '7bit',
      mimetype: 'image/jpeg',
      buffer: Buffer.from('fake image data'),
      size: 1024,
      filename: 'test-image.jpg'
    };

    // 1. Upload image
    logger.info('Step 1: Uploading image...');
    const imageUrl = await minioStorageClient.uploadImage(testImage, TEST_BUCKET);
    expect(imageUrl).toBeTruthy();
    expect(imageUrl).toContain(TEST_BUCKET);
    expect(imageUrl).toContain(testImage.filename);
    logger.info('✓ Image uploaded successfully');

    // 2. Get signed URL for image
    logger.info('Step 2: Getting signed URL for image...');
    const signedUrl = await minioStorageClient.getImage(imageUrl, TEST_BUCKET);
    expect(signedUrl).toBeTruthy();
    expect(signedUrl).toContain('X-Amz-Signature');
    logger.info('✓ Signed URL generated successfully');

    // 3. Delete image
    logger.info('Step 3: Deleting image...');
    await minioStorageClient.deleteImage(imageUrl, TEST_BUCKET);
    logger.info('✓ Image deleted successfully');

    logger.info('✓ All image operations completed successfully');
  });
}); 