// Mock implementation
const mockStore = {
  isBucketEmpty: false,
  isBucketDeleted: false,
  files: [{
    name: 'test-file.txt',
    id: 'test-file-id',
    bucket_id: 'test-bucket',
    owner: 'test-owner',
    updated_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    last_accessed_at: new Date().toISOString(),
    metadata: {},
    buckets: {
      id: 'test-bucket',
      name: 'test-bucket',
      owner: 'test-owner',
      public: false,
      file_size_limit: 1024 * 1024,
      allowed_mime_types: ['text/plain'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }]
};

// Create a mock storage instance
const mockStorage = {
  listBuckets: jest.fn().mockImplementation(() => {
    console.log('DEBUG: listBuckets called, isBucketDeleted =', mockStore.isBucketDeleted);
    if (mockStore.isBucketDeleted) {
      return Promise.resolve({ 
        data: [], 
        error: null 
      });
    }
    return Promise.resolve({ 
      data: [{
        id: 'test-id',
        name: 'test-bucket',
        owner: 'test-owner',
        public: false,
        file_size_limit: 1024 * 1024,
        allowed_mime_types: ['text/plain'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }], 
      error: null 
    });
  }),
  createBucket: jest.fn().mockImplementation(() => Promise.resolve({ 
    data: { 
      id: 'test-id',
      name: 'test-bucket',
      owner: 'test-owner',
      public: false,
      file_size_limit: 1024 * 1024,
      allowed_mime_types: ['text/plain'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }, 
    error: null 
  })),
  from: jest.fn().mockReturnValue({
    upload: jest.fn().mockImplementation(() => Promise.resolve({ 
      data: { 
        id: 'test-file-id',
        path: 'test-folder/test-file.txt',
        fullPath: 'test-bucket/test-folder/test-file.txt'
      }, 
      error: null 
    })),
    list: jest.fn().mockImplementation(() => {
      console.log('DEBUG: list called, isBucketEmpty =', mockStore.isBucketEmpty);
      const result = mockStore.isBucketEmpty ? [] : mockStore.files;
      console.log('DEBUG: list returning', result);
      return Promise.resolve({ 
        data: result,
        error: null 
      });
    }),
    download: jest.fn().mockImplementation(() => Promise.resolve({ 
      data: Buffer.from('Hello, this is a test file!'), 
      error: null 
    })),
    remove: jest.fn().mockImplementation(() => {
      console.log('DEBUG: remove called, setting isBucketEmpty to true');
      mockStore.isBucketEmpty = true;
      return Promise.resolve({ data: null, error: null });
    }),
    getPublicUrl: jest.fn().mockReturnValue({ 
      data: { publicUrl: 'http://example.com/test-file.txt' } 
    })
  }),
  emptyBucket: jest.fn().mockImplementation(() => {
    console.log('DEBUG: emptyBucket called, setting isBucketEmpty to true');
    mockStore.isBucketEmpty = true;
    console.log('DEBUG: isBucketEmpty set to', mockStore.isBucketEmpty);
    return Promise.resolve({ data: { message: 'Successfully emptied' }, error: null });
  }),
  deleteBucket: jest.fn().mockImplementation(() => {
    console.log('DEBUG: deleteBucket called, setting bucket as deleted');
    mockStore.isBucketDeleted = true;
    return Promise.resolve({ data: { message: 'Successfully deleted' }, error: null });
  }),
  getBucket: jest.fn().mockImplementation(() => {
    console.log('DEBUG: getBucket called, isBucketDeleted =', mockStore.isBucketDeleted);
    if (mockStore.isBucketDeleted) {
      return Promise.resolve({ data: null, error: { message: 'Bucket not found' } });
    }
    return Promise.resolve({ 
      data: {
        id: 'test-id',
        name: 'test-bucket',
        owner: 'test-owner',
        public: false,
        file_size_limit: 1024 * 1024,
        allowed_mime_types: ['text/plain'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, 
      error: null 
    });
  })
};

// Mock the createClient function
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    storage: mockStorage
  }))
}));

import { describe, it, expect, beforeAll, afterAll, jest, beforeEach } from '@jest/globals';
import { storageClient } from '../storage.service';

describe('Storage Service', () => {
  const TEST_BUCKET = 'test-bucket';
  const TEST_FILE_PATH = 'test-folder/test-file.txt';
  const TEST_FILE_CONTENT = 'Hello, this is a test file!';
  let testFileBuffer: Buffer;

  beforeAll(() => {
    testFileBuffer = Buffer.from(TEST_FILE_CONTENT);
  });

  beforeEach(() => {
    // Reset mock state
    mockStore.isBucketEmpty = false;
    mockStore.isBucketDeleted = false;
    jest.clearAllMocks();
  });

  afterAll(async () => {
    try {
      await storageClient.deleteBucket(TEST_BUCKET);
    } catch (error) {
      // Ignore errors during cleanup
    }
  });

  it('should perform a complete storage lifecycle', async () => {
    console.log('\n=== Starting Storage Lifecycle Test ===\n');

    // 1. Create bucket
    console.log('Step 1: Creating bucket...');
    const createResponse = await storageClient.createBucket({
      name: TEST_BUCKET,
      public: false,
      fileSizeLimit: 1024 * 1024,
      allowedMimeTypes: ['text/plain']
    });
    expect(createResponse.error).toBeNull();
    expect(createResponse.data).toBeTruthy();
    console.log('✓ Bucket created successfully\n');

    // 2. Upload file
    console.log('Step 2: Uploading file...');
    const uploadResponse = await storageClient.uploadFile(
      TEST_BUCKET,
      TEST_FILE_PATH,
      testFileBuffer,
      {
        contentType: 'text/plain',
        upsert: true
      }
    );
    expect(uploadResponse.error).toBeNull();
    expect(uploadResponse.data?.path).toBe(TEST_FILE_PATH);
    console.log('✓ File uploaded successfully\n');

    // 3. List files in bucket
    console.log('Step 3: Listing files in bucket...');
    const listResponse = await storageClient.listFiles(TEST_BUCKET, 'test-folder');
    expect(listResponse.error).toBeNull();
    expect(listResponse.data).toHaveLength(1);
    const firstFile = listResponse.data?.[0];
    expect(firstFile).toBeDefined();
    expect(firstFile?.name).toBe('test-file.txt');
    console.log('✓ Files listed successfully\n');

    // 4. Download file
    console.log('Step 4: Downloading file...');
    const downloadResponse = await storageClient.downloadFile(TEST_BUCKET, TEST_FILE_PATH);
    expect(downloadResponse.error).toBeNull();
    expect(downloadResponse.data).toBeTruthy();
    
    const downloadedContent = downloadResponse.data?.toString();
    expect(downloadedContent).toBe(TEST_FILE_CONTENT);
    console.log('✓ File downloaded successfully\n');

    // 5. Empty bucket
    console.log('Step 5: Emptying bucket...');
    console.log('Current mockStore state:', { isBucketEmpty: mockStore.isBucketEmpty });
    const emptyResponse = await storageClient.emptyBucket(TEST_BUCKET);
    expect(emptyResponse.error).toBeNull();
    expect(emptyResponse.data?.message).toBe('Successfully emptied');
    console.log('✓ Bucket emptied successfully');
    console.log('Updated mockStore state:', { isBucketEmpty: mockStore.isBucketEmpty }, '\n');

    // Verify bucket is empty
    console.log('Step 5.1: Verifying bucket is empty...');
    const listAfterEmptyResponse = await storageClient.listFiles(TEST_BUCKET);
    expect(listAfterEmptyResponse.error).toBeNull();
    expect(listAfterEmptyResponse.data).toHaveLength(0);
    console.log('✓ Bucket verified as empty\n');

    // 6. Delete bucket
    console.log('Step 6: Deleting bucket...');
    const deleteResponse = await storageClient.deleteBucket(TEST_BUCKET);
    expect(deleteResponse.error).toBeNull();
    expect(deleteResponse.data?.message).toBe('Successfully deleted');
    console.log('✓ Bucket deleted successfully\n');

    // Verify bucket is deleted
    console.log('Step 6.1: Verifying bucket is deleted...');
    const getBucketResponse = await storageClient.getBucket(TEST_BUCKET);
    expect(getBucketResponse.error).toBeTruthy();
    console.log('✓ Bucket verified as deleted\n');

    console.log('=== Storage Lifecycle Test Completed Successfully ===\n');
  }, 30000);
}); 