#!/bin/bash

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "Error: .env file not found"
    exit 1
fi

# Function to check if a service is running
check_service() {
    local service=$1
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service is running"
        return 0
    else
        echo "❌ $service is not running"
        return 1
    fi
}

# Function to check if database exists
check_database() {
    if docker exec kibbutz-postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw $POSTGRES_DB; then
        echo "✅ Database $POSTGRES_DB exists"
        return 0
    else
        echo "❌ Database $POSTGRES_DB does not exist"
        return 1
    fi
}

# Function to check if auth schema exists
check_auth_schema() {
    if docker exec kibbutz-postgres psql -U postgres -d $POSTGRES_DB -c "\dn" | grep -q "auth"; then
        echo "✅ Auth schema exists"
        return 0
    else
        echo "❌ Auth schema does not exist"
        return 1
    fi
}

# Function to check if auth tables exist
check_auth_tables() {
    local table_count=$(docker exec kibbutz-postgres psql -U postgres -d $POSTGRES_DB -c "\dt auth.*" | grep -c "auth")
    if [ $table_count -gt 0 ]; then
        echo "✅ Auth tables exist ($table_count tables found)"
        return 0
    else
        echo "❌ No auth tables found"
        return 1
    fi
}

# Function to check if Elasticsearch directories exist
check_elastic_directories() {
    if [ -d "./volumes/elasticsearch" ] && [ -d "./volumes/filebeat" ]; then
        echo "✅ Elasticsearch and Filebeat directories exist"
        return 0
    else
        echo "❌ Elasticsearch or Filebeat directories are missing"
        return 1
    fi
}

# Function to check if Elasticsearch is responding
check_elasticsearch_health() {
    if curl -s http://localhost:9200/_cluster/health | grep -q "status"; then
        echo "✅ Elasticsearch is responding"
        return 0
    else
        echo "❌ Elasticsearch is not responding"
        return 1
    fi
}

# Main execution
echo "Starting verification..."

# Check services
echo -e "\nChecking services..."
services=("postgres" "auth" "rest" "meta" "storage" "realtime" "kong" "studio" "elasticsearch" "kibana" "filebeat")
all_services_ok=true

for service in "${services[@]}"; do
    if ! check_service $service; then
        all_services_ok=false
    fi
done

# Check database and schema
echo -e "\nChecking database and schema..."
if ! check_database; then
    all_services_ok=false
fi

if ! check_auth_schema; then
    all_services_ok=false
fi

if ! check_auth_tables; then
    all_services_ok=false
fi

# Check Elasticsearch setup
echo -e "\nChecking Elasticsearch setup..."
if ! check_elastic_directories; then
    all_services_ok=false
fi

if ! check_elasticsearch_health; then
    all_services_ok=false
fi

# Final result
echo -e "\nVerification completed!"
if $all_services_ok; then
    echo "✅ All checks passed! Installation is successful."
    exit 0
else
    echo "❌ Some checks failed. Please review the output above."
    exit 1
fi 