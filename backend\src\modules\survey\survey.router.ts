import { Router } from 'express';
import { SurveyController } from './survey.controller';
import { SurveyService } from './survey.service';
import { authenticateToken } from '../../middleware/auth';
import { requireModeratorOrAdmin } from '../../middleware/rbacMiddleware';

const router = Router();
const surveyService = new SurveyService();
const surveyController = new SurveyController(surveyService);

// Public routes (no authentication required)
router.get('/', surveyController.getSurveys.bind(surveyController)); // Get all surveys
router.get('/:id', surveyController.getSurvey.bind(surveyController)); // Get specific survey

// Protected routes (authentication required)
router.post('/', authenticateToken, requireModeratorOrAdmin(), surveyController.createSurvey.bind(surveyController));
router.put('/:id', authenticateToken, requireModeratorOrAdmin(), surveyController.updateSurvey.bind(surveyController));
router.delete('/:id', authenticateToken, requireModeratorOrAdmin(), surveyController.deleteSurvey.bind(surveyController));

// Survey responses (authenticated users) - TODO: Implement survey response functionality
// router.post('/:id/responses', authenticateToken, surveyController.submitSurveyResponse.bind(surveyController));
// router.get('/:id/responses', authenticateToken, requireModeratorOrAdmin(), surveyController.getSurveyResponses.bind(surveyController));

export default router; 