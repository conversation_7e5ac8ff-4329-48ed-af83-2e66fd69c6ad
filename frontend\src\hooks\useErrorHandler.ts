import { useCallback } from 'react';

interface ErrorHandlerOptions {
  logToConsole?: boolean;
  showUserFriendlyMessage?: boolean;
  reportToService?: boolean;
}

interface UseErrorHandlerReturn {
  handleError: (error: Error, context?: string) => void;
  handleAsyncError: (asyncFn: () => Promise<any>, context?: string) => Promise<void>;
}

/**
 * Hook for consistent error handling across the application
 */
export const useErrorHandler = (options: ErrorHandlerOptions = {}): UseErrorHandlerReturn => {
  const {
    logToConsole = true,
    showUserFriendlyMessage = true,
    reportToService = false,
  } = options;

  const handleError = useCallback((error: Error, context?: string) => {
    // Log to console in development
    if (logToConsole && __DEV__) {
      console.error(`Error${context ? ` in ${context}` : ''}:`, error);
    }

    // Report to error service in production
    if (reportToService && !__DEV__) {
      // Example: Sentry.captureException(error, { tags: { context } });
      console.warn('Error reporting service not configured');
    }

    // Show user-friendly message
    if (showUserFriendlyMessage) {
      // In a real app, you might want to show a toast or alert
      // For now, we'll just log a user-friendly message
      console.warn('User-friendly error message should be shown here');
    }
  }, [logToConsole, showUserFriendlyMessage, reportToService]);

  const handleAsyncError = useCallback(async (
    asyncFn: () => Promise<any>, 
    context?: string
  ): Promise<void> => {
    try {
      await asyncFn();
    } catch (error) {
      handleError(error as Error, context);
    }
  }, [handleError]);

  return {
    handleError,
    handleAsyncError,
  };
};

/**
 * Higher-order function to wrap async functions with error handling
 */
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: string
) => {
  return async (...args: T): Promise<R | undefined> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error(`Error in ${context || 'async function'}:`, error);
      
      // In production, report to error service
      if (!__DEV__) {
        // Example: Sentry.captureException(error, { tags: { context } });
      }
      
      return undefined;
    }
  };
};
