import React from 'react';
import { StyleSheet } from 'react-native';
import { Button as PaperButton } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { rtlConfig } from '../../theme/rtl';
import { getSpacing, getTypography } from '../../theme';

interface ButtonProps {
  mode?: 'text' | 'outlined' | 'contained';
  onPress: () => void;
  children: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  style?: any;
}

export const Button = ({
  mode = 'contained',
  onPress,
  children,
  loading = false,
  disabled = false,
  style,
}: ButtonProps) => {
  const theme = useTheme();

  return (
    <PaperButton
      mode={mode}
      onPress={onPress}
      loading={loading}
      disabled={disabled}
      style={[
        styles.button,
        { backgroundColor: mode === 'contained' ? theme.colors.primary : undefined },
        style,
      ]}
      labelStyle={[
        styles.label,
        { color: mode === 'contained' ? theme.colors.onPrimary : theme.colors.primary },
      ]}
    >
      {children}
    </PaperButton>
  );
};

const styles = StyleSheet.create({
  button: {
    marginVertical: getSpacing('sm'),
    paddingVertical: getSpacing('xs'),
  },
  label: {
    ...getTypography('base', 'bold'),
    textAlign: rtlConfig.textAlign,
  },
});