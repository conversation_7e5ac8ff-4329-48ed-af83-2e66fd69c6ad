import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { Survey, surveysService } from '@services/surveys';

type SurveyDetailsScreenRouteProp = RouteProp<SurveysStackParamList, 'SurveyDetails'>;
type SurveyDetailsScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'SurveyDetails'>;

export default function SurveyDetailsScreen() {
  const { t } = useTranslation();
  const route = useRoute<SurveyDetailsScreenRouteProp>();
  const navigation = useNavigation<SurveyDetailsScreenNavigationProp>();
  const { surveyId } = route.params;

  const [survey, setSurvey] = useState<Survey | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSurvey();
  }, [surveyId]);

  const loadSurvey = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading survey details for ID:', surveyId);
      const surveyData = await surveysService.getSurveyById(surveyId);

      if (surveyData) {
        setSurvey(surveyData);
        console.log('✅ Survey loaded successfully:', surveyData.title);
      } else {
        Alert.alert('שגיאה', 'הסקר לא נמצא');
      }
    } catch (error) {
      console.error('❌ Error loading survey:', error);
      Alert.alert('שגיאה', 'שגיאה בטעינת פרטי הסקר');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const handleParticipate = () => {
    if (survey?.status === 'OPEN') {
      // Navigate to answer survey screen
      navigation.navigate('SurveyResults', { surveyId: survey.id });
    } else {
      Alert.alert('סקר סגור', 'הסקר הזה כבר נסגר ולא ניתן להשתתף בו');
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען פרטי סקר...</Text>
      </View>
    );
  }

  if (!survey) {
    return (
      <View style={styles.centerContainer}>
        <Text>הסקר לא נמצא</Text>
      </View>
    );
  }

  const isOpen = survey.status === 'OPEN';

  return (
    <View style={styles.container}>
      <Header
        title={`🏗️ ${survey.title}`}
        showBackButton={true}
      />
      <WebCompatibleScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* Survey Image */}
        {survey.imageUrl && (
          <View style={styles.imageContainer}>
            <Image source={{ uri: survey.imageUrl }} style={styles.surveyImage} />
          </View>
        )}

        {/* Survey Header */}
        <View style={styles.surveyHeader}>
          <Text style={[styles.surveyTitle, { textAlign: getTextAlign() }]}>
            {survey.title}
          </Text>
          <Text style={[styles.surveyDescription, { textAlign: getTextAlign() }]}>
            {survey.description}
          </Text>
        </View>

        {/* Survey Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>🗓️</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>סיום הסקר</Text>
              <Text style={styles.detailDescription}>
                {survey.closingDate ? formatDate(survey.closingDate) : 'ללא תאריך סיום'}
              </Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>👥</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>משתתפים עד כה</Text>
              <Text style={styles.detailDescription}>
                {survey.participantsCount} משתתפים
              </Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>📊</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>סטטוס</Text>
              <Text style={[styles.detailDescription, { color: isOpen ? getColor('success') : getColor('neutral', 500) }]}>
                {isOpen ? 'סקר פתוח' : 'סקר סגור'}
              </Text>
            </View>
          </View>
        </View>

        {/* Action Button */}
        <TouchableOpacity
          style={[
            styles.participateButton,
            { backgroundColor: isOpen ? getColor('primary') : getColor('neutral', 400) }
          ]}
          onPress={handleParticipate}
          disabled={!isOpen}
        >
          <Text style={[styles.participateButtonText, { textAlign: getTextAlign() }]}>
            {isOpen ? 'השתתפו עכשיו' : 'הסקר סגור'}
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle({
    flex: 1,
    backgroundColor: getColor('background'),
  }),
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: getColor('background'),
  },
  content: {
    flex: 1,
  },
  scrollContent: createWebCompatibleScrollContentStyle({
    flexGrow: 1,
    padding: getSpacing('lg'),
  }),
  imageContainer: {
    marginBottom: getSpacing('lg'),
    borderRadius: getBorderRadius('lg'),
    overflow: 'hidden',
  },
  surveyImage: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  surveyHeader: {
    marginBottom: getSpacing('xl'),
  },
  surveyTitle: {
    ...getTypography('headingLarge'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('sm'),
  },
  surveyDescription: {
    ...getTypography('bodyLarge'),
    color: getColor('onSurface', 700),
    lineHeight: 24,
  },
  detailsContainer: {
    marginBottom: getSpacing('xl'),
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('lg'),
    backgroundColor: getColor('surface'),
    padding: getSpacing('md'),
    borderRadius: getBorderRadius('md'),
    ...getShadow('xs'),
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: getBorderRadius('sm'),
    backgroundColor: getColor('primary', 100),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getSpacing('md'),
  },
  detailIconText: {
    fontSize: 20,
  },
  detailText: {
    flex: 1,
  },
  detailTitle: {
    ...getTypography('labelLarge'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('xs'),
  },
  detailDescription: {
    ...getTypography('bodyMedium'),
    color: getColor('onSurface', 600),
  },
  participateButton: {
    paddingVertical: getSpacing('md'),
    paddingHorizontal: getSpacing('lg'),
    borderRadius: getBorderRadius('md'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  participateButtonText: {
    ...getTypography('labelLarge'),
    color: '#fff',
    fontWeight: 'bold',
  },
});
