import { MD3LightTheme } from 'react-native-paper';
import { colors } from './colors';
import { typography } from './typography';
import { designTokens } from './designTokens';

export const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...colors,
  },
  typography,
  // Add spacing system (using design tokens)
  spacing: designTokens.spacing,
  // Add border radius (using design tokens)
  borderRadius: designTokens.borderRadius,
  // Add shadows (using design tokens)
  shadows: designTokens.shadows,
};

// Export design system
export { designTokens } from './designTokens';
export * from './utils';
export type {
  DesignTokens,
  Spacing<PERSON>ey,
  ColorKey,
  TypographyKey,
  BorderRadiusKey,
  ShadowKey
} from './designTokens';
export { createTheme } from './variants';