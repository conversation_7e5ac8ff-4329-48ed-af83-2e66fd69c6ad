import { Router } from 'express';
import { JobController } from './job.controller';
import { JobService } from './job.service';
import { authenticateToken } from '../../middleware/auth';
import { requireModeratorOrAdmin } from '../../middleware/rbacMiddleware';

const router = Router();
const jobService = new JobService();
const jobController = new JobController(jobService);

// Public routes (no authentication required)
router.get('/', jobController.getJobs.bind(jobController)); // Get all jobs
router.get('/:id', jobController.getJob.bind(jobController)); // Get specific job

// Protected routes (authentication required)
router.post('/', authenticateToken, requireModeratorOrAdmin(), jobController.createJob.bind(jobController));
router.put('/:id', authenticateToken, requireModeratorOrAdmin(), jobController.updateJob.bind(jobController));
router.delete('/:id', authenticateToken, requireModeratorOrAdmin(), jobController.deleteJob.bind(jobController));

// Job applications (authenticated users) - TODO: Implement job application functionality
// router.post('/:id/apply', authenticateToken, jobController.applyForJob.bind(jobController));
// router.delete('/:id/apply', authenticateToken, jobController.withdrawApplication.bind(jobController));

export default router; 