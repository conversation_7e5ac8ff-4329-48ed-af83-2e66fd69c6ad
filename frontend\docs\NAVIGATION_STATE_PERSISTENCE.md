# Navigation State Persistence Fix

## Problem Description

The web app was refreshing to the home screen when switching between browser tabs or applications on PC, even when the user was in the middle of creating something or navigating through the app. This was causing a poor user experience and loss of navigation context.

## Root Cause Analysis

The issue was caused by several factors:

1. **Missing `initialState` prop**: The NavigationContainer was saving navigation state but not restoring it on initialization
2. **No web-specific focus/blur handling**: The app wasn't detecting when users switched browser tabs
3. **Lack of Page Visibility API usage**: No proper detection of tab visibility changes
4. **Incomplete navigation state validation**: Saved states weren't being validated before restoration

## Solution Implementation

### 1. Navigation State Persistence (`frontend/src/App.tsx`)

- **Added proper `initialState` prop** to NavigationContainer to restore saved navigation state
- **Implemented comprehensive state saving/loading** with error handling and validation
- **Added Page Visibility API integration** to detect tab switching
- **Enhanced debugging** with detailed logging for troubleshooting

### 2. Page Visibility Hook (`frontend/src/hooks/usePageVisibility.ts`)

Created a custom hook that provides:
- **Page Visibility API detection** for browser tab switching
- **Window focus/blur event handling** as fallback
- **Combined tab visibility detection** with comprehensive browser support
- **Web platform detection** to avoid running on mobile platforms

### 3. Web Navigation Utilities (`frontend/src/utils/webNavigation.ts`)

Implemented utility functions for:
- **Safe navigation state saving/loading** with AsyncStorage and localStorage fallback
- **Navigation state validation** to ensure data integrity
- **Browser navigation handling** for back/forward button support
- **Page unload handling** to save state before browser closes
- **Debug utilities** for troubleshooting navigation issues

### 4. Debug Component (`frontend/src/components/NavigationStateDebugger.tsx`)

Added a development-only debug component that:
- **Shows current navigation state** in real-time
- **Provides navigation testing buttons** to switch between screens
- **Displays debug information** about saved vs current state
- **Only appears on web platform in development mode**

## Key Features

### Automatic State Persistence
- Navigation state is automatically saved on every navigation change
- State is restored when the app initializes
- Fallback to localStorage for page unload scenarios

### Browser Tab Switching Detection
- Uses Page Visibility API to detect when user switches tabs
- Saves current state when tab becomes inactive
- Prevents navigation reset when returning to the tab

### Comprehensive Error Handling
- Validates navigation state before restoration
- Graceful fallback when saved state is invalid or corrupted
- Detailed logging for debugging issues

### Cross-Platform Compatibility
- Web-specific features only run on web platform
- Mobile platforms continue to work normally
- No performance impact on mobile apps

## Testing Instructions

### Manual Testing

1. **Start the web app**:
   ```bash
   cd frontend
   npm run web
   ```

2. **Navigate to different screens**:
   - Go to Events screen
   - Navigate to Create Event or any sub-screen
   - Fill out some form data (if applicable)

3. **Test tab switching**:
   - Switch to another browser tab
   - Wait a few seconds
   - Switch back to the app tab
   - **Expected**: App should remain on the same screen with same state

4. **Test browser refresh**:
   - Navigate to a specific screen (not home)
   - Refresh the browser (F5 or Ctrl+R)
   - **Expected**: App should restore to the same screen

5. **Use the debug component** (development mode only):
   - Look for the debug panel in the top-right corner on the home screen
   - Use "Go to Events" and "Go to Jobs" buttons to navigate
   - Use "Debug Current" and "Debug Saved" to inspect state
   - Check browser console for detailed logging

### Debug Logging

The implementation includes comprehensive logging:

```javascript
// Navigation state restoration
🔄 Restoring navigation state
🔍 [RESTORE] Navigation state: { routesCount: 2, currentIndex: 1, currentRoute: "Events" }

// State changes
🔍 [STATE_CHANGE] Navigation state: { routesCount: 2, currentIndex: 0, currentRoute: "Home" }

// Page visibility changes
🔄 Page visibility changed: hidden
🔄 Page visibility changed: visible

// State saving/loading
💾 Navigation state saved successfully
📱 Navigation state loaded successfully
```

## Configuration Options

### Navigation State Expiration
States older than 24 hours are automatically cleared:

```typescript
const maxAge = 24 * 60 * 60 * 1000; // 24 hours
```

### Debug Mode
Debug component only shows in development:

```typescript
const shouldShow = Platform.OS === 'web' && __DEV__;
```

### Storage Keys
Navigation state is stored with versioned keys:

```typescript
const NAVIGATION_PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';
```

## Browser Compatibility

### Page Visibility API Support
- **Chrome**: Full support
- **Firefox**: Full support  
- **Safari**: Full support
- **Edge**: Full support
- **IE**: Not supported (graceful fallback to window focus events)

### Fallback Mechanisms
1. Page Visibility API (preferred)
2. Window focus/blur events (fallback)
3. localStorage (emergency fallback for page unload)

## Performance Considerations

- **Async state saving**: Navigation state is saved asynchronously to avoid blocking UI
- **Debounced logging**: Debug logging is optimized to avoid console spam
- **Minimal memory usage**: Only current and initial states are kept in memory
- **Platform detection**: Web-specific code only runs on web platform

## Troubleshooting

### Common Issues

1. **State not restoring**: Check browser console for validation errors
2. **Debug component not showing**: Ensure you're in development mode on web
3. **Tab switching not detected**: Verify Page Visibility API support in browser
4. **Performance issues**: Check if debug logging is disabled in production

### Debug Commands

```javascript
// Check current navigation state
debugNavigationState(currentState, 'MANUAL_CHECK');

// Clear all saved state
await clearNavigationState();

// Load saved state manually
const state = await loadNavigationState();
```

## Future Enhancements

1. **Form data persistence**: Save form inputs during navigation
2. **Scroll position restoration**: Remember scroll positions on screens
3. **Deep linking support**: Handle URL-based navigation state
4. **State compression**: Compress large navigation states for storage efficiency
5. **Analytics integration**: Track navigation patterns for UX improvements
