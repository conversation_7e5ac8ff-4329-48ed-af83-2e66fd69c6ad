<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community App - Food Market</title>
    <style>
    <style>
 * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 350px;
            position: relative;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .header-action {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .header-action:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        .search-bar {
            background: #f8f9fa;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            width: 100%;
            margin-bottom: 20px;
            font-size: 14px;
            outline: none;
            transition: box-shadow 0.2s;
        }
        
        .search-bar:focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }
        
        .market-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }
        
        .market-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            flex-shrink: 0;
            background: #f9ca24;
        }
        
        .card-text {
            flex: 1;
        }
        
        .card-text h3 {
            font-size: 16px;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .card-text p {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }
        
        .price-tag {
            background: #27ae60;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: transform 0.2s;
            border: none;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            padding: 10px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #7f8c8d;
        }
        
        .nav-item:hover {
            transform: scale(1.1);
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Add modal styles for selling item */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal {
            background: white;
            border-radius: 15px;
            padding: 30px;
            width: 90%;
            max-width: 300px;
            text-align: center;
        }
        
        .modal h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .modal p {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .modal-buttons {
            display: flex;
            gap: 10px;
        }
        
        .modal-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.2s;
        }
        
        .modal-button.primary {
            background: #667eea;
            color: white;
        }
        
        .modal-button.primary:hover {
            background: #5a67d8;
        }
        
        .modal-button.secondary {
            background: #f8f9fa;
            color: #2c3e50;
        }
        
        .modal-button.secondary:hover {
            background: #e9ecef;
        }
        
        /* Category filter */
        .category-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .category-filter {
            background: #f8f9fa;
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .category-filter.active {
            background: #667eea;
            color: white;
        }
        
        .category-filter:hover:not(.active) {
            background: #e9ecef;
        }
</style>

    </style>
</head>
<body>
<div class="container">
  <h1>🍲 Community Food Market</h1>
  <div class="mockups-grid">

    <!-- Food Market Listing Page -->
    <div class="phone-mockup">
      <div class="screen-label">FOOD MARKET</div>
      <div class="screen">
        <div class="status-bar">
          <span>9:41</span>
          <span>🔋 100%</span>
        </div>
        <div class="app-header">
          <div class="header-title">🍲 Food Market</div>
          <button class="header-action">New</button>
        </div>
        <div class="content">
          <input type="text" class="search-bar" placeholder="🔍 Search food items..." />

          <div class="market-card">
            <div class="card-icon">🥣</div>
            <div class="card-text">
              <h3>Homemade Soup</h3>
              <p>Warm vegetable soup by Sarah J. Pick up after 5 PM at House 12.</p>
            </div>
            <div class="price-tag">$5</div>
          </div>

          <div class="market-card">
            <div class="card-icon">🧁</div>
            <div class="card-text">
              <h3>Mini Cupcakes</h3>
              <p>12-pack baked by Dana G. Available near the school gate between 3-4 PM.</p>
            </div>
            <div class="price-tag">$8</div>
          </div>
        </div>
        <div class="bottom-nav">
          <div class="nav-item"><div>🏠</div><div class="nav-label">Home</div></div>
          <div class="nav-item"><div>👥</div><div class="nav-label">People</div></div>
          <div class="nav-item active"><div>🍲</div><div class="nav-label">Food</div></div>
          <div class="nav-item"><div>⚙️</div><div class="nav-label">More</div></div>
        </div>
      </div>
    </div>

    <!-- Publish New Food Page -->
    <div class="phone-mockup">
      <div class="screen-label">PUBLISH FOOD</div>
      <div class="screen">
        <div class="status-bar">
          <span>9:41</span>
          <span>🔋 100%</span>
        </div>
        <div class="app-header">
          <div class="header-title">📤 Offer Food</div>
          <button class="header-action">Back</button>
        </div>
        <div class="content">
          <div class="form-group">
            <input type="text" class="search-bar" placeholder="Food Name" value="Mini Pizzas" />
          </div>
          <div class="form-group">
            <input type="text" class="search-bar" placeholder="Short Description" value="Baked with cheese and olives" />
          </div>
          <div class="form-group">
            <input type="text" class="search-bar" placeholder="Price" value="$3 each" />
          </div>
          <div class="form-group">
            <input type="text" class="search-bar" placeholder="Seller Name" value="Yossi Cohen" />
          </div>
          <div class="form-group">
            <input type="text" class="search-bar" placeholder="Pickup Details" value="Available 18:00-20:00 at House 24" />
          </div>
          <div class="form-group">
            <textarea class="search-bar" style="height:100px;">Freshly made. Perfect for kids' dinner or snacks.</textarea>
          </div>
          <button class="modal-button primary" style="margin-top: 10px;">Post Food Offer</button>
        </div>
      </div>
    </div>

  </div>
</div>
</body>
</html>
