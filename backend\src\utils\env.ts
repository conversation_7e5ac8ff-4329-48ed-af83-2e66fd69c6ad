import fs from 'fs';
import path from 'path';

interface EnvVariable {
  name: string;
  description: string;
  defaultValue?: string;
  required: boolean;
  module: string;
}

class EnvRegistry {
  private static instance: EnvRegistry;
  private variables: Map<string, EnvVariable> = new Map();

  private constructor() {}

  static getInstance(): EnvRegistry {
    if (!EnvRegistry.instance) {
      EnvRegistry.instance = new EnvRegistry();
    }
    return EnvRegistry.instance;
  }

  register(variable: EnvVariable) {
    this.variables.set(variable.name, variable);
    console.debug(`Registered environment variable: ${variable.name} from module: ${variable.module}`);
  }

  registerMultiple(variables: EnvVariable[]) {
    variables.forEach(variable => this.register(variable));
  }

  generateEnvExample() {
    try {
      const envExamplePath = path.join(process.cwd(), '.env.example');
      
      // Group variables by module
      const groupedVars = new Map<string, EnvVariable[]>();
      this.variables.forEach(variable => {
        if (!groupedVars.has(variable.module)) {
          groupedVars.set(variable.module, []);
        }
        groupedVars.get(variable.module)!.push(variable);
      });

      // Generate content
      let content = '# Environment Variables\n\n';
      
      // Add required variables first
      content += '# Required Variables\n';
      this.variables.forEach(variable => {
        if (variable.required) {
          content += `# ${variable.description}\n`;
          content += `${variable.name}=${variable.defaultValue || ''}\n\n`;
        }
      });

      // Add optional variables by module
      content += '\n# Optional Variables\n';
      groupedVars.forEach((variables, module) => {
        content += `\n# ${module}\n`;
        variables.forEach(variable => {
          if (!variable.required) {
            content += `# ${variable.description}\n`;
            content += `${variable.name}=${variable.defaultValue || ''}\n\n`;
          }
        });
      });

      // Write to file
      fs.writeFileSync(envExamplePath, content);
      console.info('Generated .env.example file successfully');
    } catch (error) {
      console.error('Error generating .env.example file:', error);
      throw error;
    }
  }
}

export const envRegistry = EnvRegistry.getInstance();

// Example usage in modules:
/*
import { envRegistry } from '../utils/env';

// Register environment variables for a module
envRegistry.registerMultiple([
  {
    name: 'DATABASE_URL',
    description: 'PostgreSQL database connection string',
    defaultValue: 'postgresql://user:password@localhost:5432/kibbutz_db?schema=public',
    required: true,
    module: 'Database'
  },
  {
    name: 'SUPABASE_PUBLIC_URL',
    description: 'Supabase project URL',
    required: true,
    module: 'Supabase'
  }
]);
*/

// Function to generate .env.example at the end of initialization
export const generateEnvExample = () => {
  envRegistry.generateEnvExample();
}; 