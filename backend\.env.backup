# Environment Variables

POSTGRES_DB=kibbutz_db 

# Required Variables
# PostgreSQL database connection string
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/${POSTGRES_DB}?schema=public

# Optional Variables

# Server
# Port number for the server
PORT=3002

# Environment (development, production, test)
NODE_ENV=development

# Frontend URL for CORS configuration
FRONTEND_URL=http://localhost:3000

# Rate limiting window in milliseconds
RATE_LIMIT_WINDOW_MS=900000

# Maximum number of requests per window
RATE_LIMIT_MAX_REQUESTS=100


# Logging
# Global logging level (fatal, error, warn, info, debug)
LOG_LEVEL=info

# Enable debug logging for logger configuration
LOG_DEBUG=false

# Job module logging level
LOG_LEVEL_EVENT=debug

# Job module logging level
LOG_LEVEL_JOB=info

# Job module logging level
LOG_LEVEL_CHAT=debug

# Auth module logging level
LOG_LEVEL_AUTH=info

# Maintenance module logging level
LOG_LEVEL_MAINTENANCE=info

# Common module logging level
LOG_LEVEL_COMMON=info

# Supabase module logging level
LOG_LEVEL_SUPABASE=info

# Database module logging level
LOG_LEVEL_DATABASE=info


# Database

# Supabase
# Supabase service role key

# Supabase project URL
SUPABASE_PUBLIC_URL=http://localhost:8000

# Supabase anonymous key
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

#SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJhdWQiOiJhdXRoZW50aWNhdGVkIiwiaWF0IjoxNzQ5MzkyODUyLCJleHAiOjE3ODA5Mjg4NTJ9.qKU8k4g69I0ilx9e7UDJubJyNAcW5QG4e3HEbOVJ3y0

# Supabase JWT secret
SUPABASE_JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-for-kibbutz-app

# Supabase auth token expiry in seconds
SUPABASE_AUTH_EXPIRY=3600

# Supabase auth refresh token expiry in seconds
SUPABASE_AUTH_REFRESH_EXPIRY=604800

# Supabase auth cookie name
SUPABASE_AUTH_COOKIE_NAME=sb-auth-token

# Supabase auth cookie domain
SUPABASE_AUTH_COOKIE_DOMAIN=

# Whether to use secure cookies (set to false for local development)
SUPABASE_AUTH_COOKIE_SECURE=false

# Supabase auth cookie same-site policy
SUPABASE_AUTH_COOKIE_SAME_SITE=lax


# Docker Compose Environment Variables for Supabase Services

# PostgreSQL Configuration
POSTGRES_PASSWORD=postgres
JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-for-kibbutz-app

# API URLs
API_EXTERNAL_URL=http://localhost:8000
SUPABASE_PUBLIC_URL=http://localhost:8000
SITE_URL=http://localhost:3000

# Auth Configuration
DISABLE_SIGNUP=false
ENABLE_EMAIL_SIGNUP=false
ENABLE_EMAIL_AUTOCONFIRM=true
JWT_EXPIRY=3600
ADDITIONAL_REDIRECT_URLS=""

# Email Configuration (optional - for production)
SMTP_ADMIN_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_SENDER_NAME=Kibbutz App

# Email URL Paths
MAILER_URLPATHS_INVITE=/auth/v1/verify
MAILER_URLPATHS_CONFIRMATION=/auth/v1/verify
MAILER_URLPATHS_RECOVERY=/auth/v1/verify
MAILER_URLPATHS_EMAIL_CHANGE=/auth/v1/verify

# Studio Configuration
STUDIO_DEFAULT_ORGANIZATION=Kibbutz Community
STUDIO_DEFAULT_PROJECT=Kibbutz App

# API Keys

ENABLE_EMAIL_SIGNUP=false

# Image Processing
IMGPROXY_ENABLE_WEBP_DETECTION=true