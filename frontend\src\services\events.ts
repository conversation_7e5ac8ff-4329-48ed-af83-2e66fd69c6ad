// Events Service - Handles event operations
import { apiService } from '@services';
import { supabase } from '@lib/supabase';

export interface Event {
  id: string;
  title: string;
  description: string;
  date: Date;
  location: string;
  type: 'community' | 'tenant';
  icon?: string;
  imageUrl?: string;
  attendees?: number;
  createdBy: string;
  createdAt: Date;
}

export interface CreateEventData {
  title: string;
  description: string;
  date: Date;
  location: string;
  type: 'community' | 'tenant';
  icon?: string;
  imageUrl?: string;
}

class EventsService {
  // Option 1: Use Custom Backend (Recommended for business logic)
  async getEventsFromBackend(): Promise<Event[]> {
    try {
      console.log('🔄 Fetching events from custom backend...');
      const response = await apiService.getEvents();

      if (response.error) {
        throw new Error(response.error);
      }

      // Transform the data to ensure dates are Date objects and add missing fields
      const events = (response.data || []).map((event: any) => ({
        ...event,
        date: new Date(event.date),
        createdAt: new Date(event.createdAt),
        rsvp: event.rsvp || [],
        comments: event.comments || [],
        reactions: event.reactions || []
      }));

      return events;
    } catch (error) {
      console.error('❌ Error fetching events from backend:', error);
      throw error;
    }
  }

  async createEventViaBackend(eventData: CreateEventData): Promise<Event> {
    try {
      console.log('🔄 Creating event via custom backend...');
      const response = await apiService.createEvent(eventData);

      if (response.error) {
        throw new Error(response.error);
      }

      // Transform the response to ensure dates are Date objects and add missing fields
      const event = {
        ...response.data,
        date: new Date(response.data.date),
        createdAt: new Date(response.data.createdAt),
        rsvp: response.data.rsvp || [],
        comments: response.data.comments || [],
        reactions: response.data.reactions || []
      };

      return event;
    } catch (error) {
      console.error('❌ Error creating event via backend:', error);
      throw error;
    }
  }

  // Option 2: Direct PostgREST (For simple CRUD operations)
  async getEventsFromSupabase(): Promise<Event[]> {
    try {
      console.log('🔄 Fetching events from Supabase...');
      const { data, error } = await supabase
        .from('Event')
        .select('*')
        .order('date', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error fetching events from Supabase:', error);
      throw error;
    }
  }

  async createEventViaSupabase(eventData: CreateEventData): Promise<Event> {
    try {
      console.log('🔄 Creating event via Supabase...');

      // Skip Supabase direct creation since we don't have user context here
      // This should only be used via backend API with proper authentication
      throw new Error('Direct Supabase creation not supported - use backend API');
    } catch (error) {
      console.error('❌ Error creating event via Supabase:', error);
      throw error;
    }
  }

  // Use backend API with token authentication
  async getEvents(): Promise<Event[]> {
    try {
      return await this.getEventsFromBackend();
    } catch (error) {
      console.error('❌ Failed to fetch events from backend:', error);
      throw new Error('Failed to fetch events');
    }
  }

  async createEvent(eventData: CreateEventData): Promise<Event> {
    try {
      // Use backend API with token authentication
      return await this.createEventViaBackend(eventData);
    } catch (error) {
      console.error('❌ Failed to create event via backend:', error);
      throw new Error('Failed to create event');
    }
  }

  async getEventById(eventId: string): Promise<Event | null> {
    try {
      console.log('🔄 Fetching event by ID:', eventId);
      const response = await apiService.getEvent(eventId);

      if (response.error) {
        throw new Error(response.error);
      }

      if (!response.data) {
        return null;
      }

      // Transform the response to ensure dates are Date objects and add missing fields
      const event = {
        ...response.data,
        date: new Date(response.data.date),
        createdAt: new Date(response.data.createdAt),
        rsvp: response.data.rsvp || [],
        comments: response.data.comments || [],
        reactions: response.data.reactions || []
      };

      return event;
    } catch (error) {
      console.error('❌ Failed to fetch event by ID:', error);
      throw new Error('Failed to fetch event');
    }
  }

  // Real-time subscriptions (only available via Supabase)
  subscribeToEvents(callback: (events: Event[]) => void) {
    console.log('🔄 Setting up real-time event subscription...');
    
    const subscription = supabase
      .channel('events')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'Event' },
        (payload) => {
          console.log('📡 Real-time event update:', payload);
          // Refetch events when changes occur
          this.getEvents().then(callback).catch(console.error);
        }
      )
      .subscribe();

    return () => {
      console.log('🔄 Unsubscribing from events...');
      subscription.unsubscribe();
    };
  }
}

export const eventsService = new EventsService();
export default eventsService;
