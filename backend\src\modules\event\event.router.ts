import { Router } from 'express';
import { EventController } from './event.controller';
import { EventService } from './event.service';
import { authenticateToken } from '../../middleware/auth';
import { requireModeratorOrAdmin } from '../../middleware/rbacMiddleware';
import { upload } from '../../middleware/upload.middleware';

const router = Router();
const eventService = new EventService();
const eventController = new EventController(eventService);

// Public routes (no authentication required)
router.get('/', eventController.getEvents.bind(eventController)); // Get all events
router.get('/:id', eventController.getEvent.bind(eventController)); // Get specific event

// Protected routes (authentication required)
router.post('/', authenticateToken, requireModeratorOrAdmin(), upload.single('image'), eventController.createEvent.bind(eventController));
router.put('/:id', authenticateToken, requireModeratorOrAdmin(), upload.single('image'), eventController.updateEvent.bind(eventController));
router.delete('/:id', authenticateToken, requireModeratorOrAdmin(), eventController.deleteEvent.bind(eventController));

// Event participation (authenticated users) - TODO: Implement event participation functionality
// router.post('/:id/participate', authenticateToken, eventController.participateInEvent.bind(eventController));
// router.delete('/:id/participate', authenticateToken, eventController.leaveEvent.bind(eventController));

export default router; 