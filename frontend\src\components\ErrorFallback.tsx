import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button, Icon } from 'react-native-paper';

interface ErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
  message?: string;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  onRetry, 
  message = 'משהו השתבש' 
}) => {
  return (
    <View style={styles.container}>
      <Icon source="alert-circle-outline" size={64} color="#d32f2f" />
      <Text style={styles.title}>{message}</Text>
      <Text style={styles.subtitle}>
        אנא נסה שוב או פנה לתמיכה
      </Text>
      
      {onRetry && (
        <Button 
          mode="contained" 
          onPress={onRetry}
          style={styles.retryButton}
        >
          נסה שוב
        </Button>
      )}

      {__DEV__ && error && (
        <View style={styles.debugContainer}>
          <Text style={styles.debugText}>
            {error.message}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
    color: '#d32f2f',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#666',
  },
  retryButton: {
    marginTop: 16,
    minWidth: 120,
  },
  debugContainer: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    maxWidth: '100%',
  },
  debugText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: '#d32f2f',
  },
});

export default ErrorFallback;
