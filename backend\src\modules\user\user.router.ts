import { Router } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { UserController } from './user.controller';
import { requireModeratorOrAdmin, requireAdmin } from '../../middleware/rbacMiddleware';

const router = Router();
const controller = new UserController();

// Get user count (no auth required for registration flow)
router.get('/count', controller.getUserCount.bind(controller));

// Get all users (admin only)
router.get('/', authenticateToken, requireAdmin(), controller.getAllUsers.bind(controller));

// Get users by type or role (for debug randomization)
router.get('/by-type-or-role', authenticateToken, requireAdmin(), controller.getUsersByTypeOrRole.bind(controller));

// Get people (open to all users)
router.get('/people', controller.getPeople.bind(controller));

// Get user by ID
router.get('/:id', authenticateToken/*, requireModeratorOrAdmin()*/, controller.getUserById.bind(controller));

// Create a user profile (after Supabase auth)
router.post('/', authenticateToken/*, requireModeratorOrAdmin()*/, controller.createUserProfile.bind(controller));

// Create a new user (admin only)
router.post('/admin', authenticateToken, requireAdmin(), controller.createUser.bind(controller));

// Update user
router.put('/:id', authenticateToken, requireModeratorOrAdmin(), controller.updateUser.bind(controller));

// Delete user
router.delete('/:id', authenticateToken, requireAdmin(), controller.deleteUser.bind(controller));

// Get user UI preferences
router.get('/:id/ui-preferences', authenticateToken, requireModeratorOrAdmin(), controller.getUserUIPreferences.bind(controller));

// Update user UI preferences
router.put('/:id/ui-preferences', authenticateToken, requireModeratorOrAdmin(), controller.updateUIPreferences.bind(controller));

export default router;