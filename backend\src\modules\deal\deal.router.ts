import { Router } from 'express';
import { DealController } from './deal.controller';
import { DealService } from './deal.service';
import { authenticateToken } from '../../middleware/auth';
import { requireModeratorOrAdmin } from '../../middleware/rbacMiddleware';

const router = Router();
const dealService = new DealService();
const dealController = new DealController(dealService);

// Public routes (no authentication required)
router.get('/', dealController.getDeals.bind(dealController)); // Get all deals
router.get('/:id', dealController.getDeal.bind(dealController)); // Get specific deal

// Protected routes (authentication required)
router.post('/', authenticateToken, requireModeratorOrAdmin(), dealController.createDeal.bind(dealController));
router.put('/:id', authenticateToken, requireModeratorOrAdmin(), dealController.updateDeal.bind(dealController));
router.delete('/:id', authenticateToken, requireModeratorOrAdmin(), dealController.deleteDeal.bind(dealController));

// Deal participation (authenticated users) - TODO: Implement deal participation functionality
// router.post('/:id/participate', authenticateToken, dealController.participateInDeal.bind(dealController));
// router.delete('/:id/participate', authenticateToken, dealController.leaveDeal.bind(dealController));

export default router; 