// Surveys Service - Handles survey operations
import { apiService } from './api';
import { supabase } from '../lib/supabase';

export interface SurveyQuestion {
  id: string;
  text: string;
  type: 'text' | 'single-choice' | 'multiple-choice';
  options?: string[];
  required?: boolean;
}

export interface Survey {
  id: string;
  title: string;
  description: string;
  questions: SurveyQuestion[];
  status: 'DRAFT' | 'OPEN' | 'CLOSED';
  createdBy: string;
  createdAt: Date;
  communityId: string;
  participantsCount: number;
  closingDate: Date | null;
  imageUrl: string | null;
  dataAiHint: string | null;
}

export interface CreateSurveyData {
  title: string;
  description: string;
  questions: SurveyQuestion[];
  status: 'DRAFT' | 'OPEN' | 'CLOSED';
  closingDate?: Date;
  imageUrl?: string;
  dataAiHint?: string;
}

export interface SurveyResponse {
  surveyId: string;
  userId: string;
  answers: { [questionId: string]: any };
  submittedAt: Date;
}

class SurveysService {
  // Use Custom Backend (Recommended for business logic)
  async getSurveysFromBackend(): Promise<Survey[]> {
    try {
      console.log('🔄 Fetching surveys from custom backend...');
      const response = await apiService.getSurveys();

      if (response.error) {
        throw new Error(response.error);
      }

      // Transform the data to ensure dates are Date objects and add missing fields
      const surveys = (response.data || []).map((survey: any) => ({
        ...survey,
        createdAt: new Date(survey.createdAt),
        closingDate: survey.closingDate ? new Date(survey.closingDate) : null,
        questions: survey.questions || [],
        participantsCount: survey.participantsCount || 0
      }));

      return surveys;
    } catch (error) {
      console.error('❌ Error fetching surveys from backend:', error);
      throw error;
    }
  }

  async getSurveyByIdFromBackend(surveyId: string): Promise<Survey | null> {
    try {
      console.log('🔄 Fetching survey from custom backend:', surveyId);
      const response = await apiService.getSurvey(surveyId);

      if (response.error) {
        throw new Error(response.error);
      }

      if (!response.data) {
        return null;
      }

      // Transform the data to ensure dates are Date objects
      const survey = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        closingDate: response.data.closingDate ? new Date(response.data.closingDate) : null,
        questions: response.data.questions || [],
        participantsCount: response.data.participantsCount || 0
      };

      return survey;
    } catch (error) {
      console.error('❌ Error fetching survey from backend:', error);
      throw error;
    }
  }

  async createSurveyViaBackend(surveyData: CreateSurveyData): Promise<Survey> {
    try {
      console.log('🔄 Creating survey via backend...');
      const response = await apiService.createSurvey({
        ...surveyData,
        communityId: '1', // Static community ID as per user preference
        createdAt: new Date().toISOString(),
        participantsCount: 0
      });

      if (response.error) {
        throw new Error(response.error);
      }

      // Transform the response
      const survey = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        closingDate: response.data.closingDate ? new Date(response.data.closingDate) : null,
        questions: response.data.questions || [],
        participantsCount: response.data.participantsCount || 0
      };

      return survey;
    } catch (error) {
      console.error('❌ Error creating survey via backend:', error);
      throw error;
    }
  }

  // Use backend API with token authentication
  async getSurveys(): Promise<Survey[]> {
    try {
      return await this.getSurveysFromBackend();
    } catch (error) {
      console.error('❌ Failed to fetch surveys from backend:', error);
      throw new Error('Failed to fetch surveys');
    }
  }

  async getSurveyById(surveyId: string): Promise<Survey | null> {
    try {
      return await this.getSurveyByIdFromBackend(surveyId);
    } catch (error) {
      console.error('❌ Failed to fetch survey from backend:', error);
      throw new Error('Failed to fetch survey');
    }
  }

  async createSurvey(surveyData: CreateSurveyData): Promise<Survey> {
    try {
      return await this.createSurveyViaBackend(surveyData);
    } catch (error) {
      console.error('❌ Failed to create survey:', error);
      throw new Error('Failed to create survey');
    }
  }

  // Real-time subscriptions (only available via Supabase)
  subscribeToSurveys(callback: (surveys: Survey[]) => void) {
    console.log('🔄 Setting up real-time survey subscription...');
    
    const subscription = supabase
      .channel('surveys')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'Survey' },
        (payload) => {
          console.log('📡 Real-time survey update:', payload);
          // Refetch surveys when changes occur
          this.getSurveys().then(callback).catch(console.error);
        }
      )
      .subscribe();

    return () => {
      console.log('🔄 Unsubscribing from surveys...');
      subscription.unsubscribe();
    };
  }
}

export const surveysService = new SurveysService();
export default surveysService;
