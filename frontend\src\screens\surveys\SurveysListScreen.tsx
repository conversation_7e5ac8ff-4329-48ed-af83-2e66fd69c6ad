import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import SearchBarWithBack from '@components/SearchBarWithBack';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { HEADER_ICONS } from '@constants';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { Survey, surveysService } from '@services/surveys';
import { useAuth } from '@contexts/AuthContext';

type SurveysListScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'SurveysList'>;

export default function SurveysListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<SurveysListScreenNavigationProp>();
  const { user } = useAuth();
  const [surveys, setSurveys] = useState<Survey[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const isAdmin = user?.role === 'ADMIN';

  const loadSurveys = async () => {
    try {
      console.log('🔄 Loading surveys...');
      const fetchedSurveys = await surveysService.getSurveys();
      setSurveys(fetchedSurveys);
      console.log('✅ Surveys loaded successfully:', fetchedSurveys.length);
    } catch (error) {
      console.error('❌ Error loading surveys:', error);
      // Fallback to mock data if backend fails
      const mockSurveys: Survey[] = [
        {
          id: '1',
          title: 'מרכז קהילתי חדש',
          description: 'עזרו לנו לתכנן את המרכז הקהילתי הבא. ענו על מספר שאלות קצרות והשפיעו!',
          questions: [],
          status: 'OPEN',
          createdBy: 'admin',
          createdAt: new Date(),
          communityId: '1',
          participantsCount: 142,
          closingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          imageUrl: null,
          dataAiHint: null,
        },
        {
          id: '2',
          title: 'אירועי קיץ 2025',
          description: 'ספרו לנו אילו פעילויות אתם הכי רוצים לראות הקיץ.',
          questions: [],
          status: 'CLOSED',
          createdBy: 'admin',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
          communityId: '1',
          participantsCount: 89,
          closingDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          imageUrl: null,
          dataAiHint: null,
        },
      ];
      setSurveys(mockSurveys);
      console.log('⚠️ Using mock data due to API failure');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadSurveys();
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadSurveys();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadSurveys();
  };

  const handleAddSurvey = () => {
    navigation.navigate('CreateSurvey');
  };

  const handleSurveyPress = (surveyId: string) => {
    navigation.navigate('SurveyDetails', { surveyId });
  };

  const filteredSurveys = surveys.filter(survey =>
    survey.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    survey.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const isOpen = status === 'OPEN';
    return (
      <View style={[styles.statusBadge, { backgroundColor: isOpen ? getColor('success') : getColor('neutral', 500) }]}>
        <Text style={styles.statusText}>
          {isOpen ? 'סקר פתוח' : 'סקר סגור'}
        </Text>
      </View>
    );
  };

  const renderSurveyItem = ({ item }: { item: Survey }) => (
    <TouchableOpacity
      style={styles.surveyCard}
      onPress={() => handleSurveyPress(item.id)}
    >
      <View style={styles.surveyIconContainer}>
        <Text style={styles.surveyIcon}>
          {item.status === 'OPEN' ? '🏗️' : '🎉'}
        </Text>
      </View>
      <View style={styles.surveyContent}>
        <Text style={[styles.surveyTitle, { textAlign: getTextAlign() }]}>
          {item.title}
        </Text>
        <Text style={[styles.surveyDescription, { textAlign: getTextAlign() }]}>
          {item.description}
        </Text>
        <Text style={[styles.surveyMeta, { textAlign: getTextAlign() }]}>
          👥 משתתפים עד כה: {item.participantsCount}
        </Text>
      </View>
      {getStatusBadge(item.status)}
    </TouchableOpacity>
  );

  // Add button for admin users
  const addButton = isAdmin ? (
    <TouchableOpacity
      style={styles.headerButton}
      onPress={handleAddSurvey}
    >
      <Icon
        name="add"
        iconSet="MaterialIcons"
        size={24}
        color="#fff"
      />
    </TouchableOpacity>
  ) : null;

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען סקרים...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="📝 סקרים"
        showBackButton={true}
        rightComponent={addButton}
      />

      <SearchBarWithBack
        placeholder="חיפוש סקרים..."
        value={searchQuery}
        onChangeText={setSearchQuery}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <FlatList
          data={filteredSurveys}
          renderItem={renderSurveyItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle({
    flex: 1,
    backgroundColor: getColor('background'),
  }),
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: getColor('background'),
  },
  content: {
    flex: 1,
  },
  scrollContent: createWebCompatibleScrollContentStyle({
    flexGrow: 1,
  }),
  listContent: {
    padding: getSpacing('md'),
  },
  surveyCard: {
    backgroundColor: getColor('surface'),
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('md'),
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...getShadow('sm'),
  },
  surveyIconContainer: {
    width: 50,
    height: 50,
    borderRadius: getBorderRadius('md'),
    backgroundColor: getColor('primary', 100),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getSpacing('md'),
  },
  surveyIcon: {
    fontSize: 24,
  },
  surveyContent: {
    flex: 1,
    marginRight: getSpacing('md'),
  },
  surveyTitle: {
    ...getTypography('headingSmall'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('xs'),
  },
  surveyDescription: {
    ...getTypography('bodyMedium'),
    color: getColor('onSurface', 700),
    marginBottom: getSpacing('sm'),
    lineHeight: 20,
  },
  surveyMeta: {
    ...getTypography('bodySmall'),
    color: getColor('onSurface', 500),
  },
  statusBadge: {
    paddingHorizontal: getSpacing('sm'),
    paddingVertical: getSpacing('xs'),
    borderRadius: getBorderRadius('sm'),
    alignSelf: 'flex-start',
  },
  statusText: {
    ...getTypography('labelSmall'),
    color: '#fff',
    fontWeight: 'bold',
  },
  headerButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
