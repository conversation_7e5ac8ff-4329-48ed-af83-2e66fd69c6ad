import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import type { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { CompositeNavigationProp } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { getTextAlign, createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { useAuth } from '@contexts';
import { RootStackParamList } from '@navigation/types';
import { WebCompatibleScrollView, Icon } from '@components';

import { FEATURE_ICONS, HEADER_ICONS } from '@constants';
import {
  createHeaderStyle,
  createHeaderButtonStyle,
  getSpacing,
  getColor,
  getBorderRadius,
  getShadow,
  getTypography
} from '../../theme';

type TabParamList = {
  Home: undefined;
  Events: undefined;
  Jobs: undefined;
  People: undefined;
  Chat: undefined;
  Surveys: undefined;
  Food: undefined;
};

type HomeScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<TabParamList>,
  StackNavigationProp<RootStackParamList>
>;

export default function HomeScreen() {
  const { t } = useTranslation();
  const { logout } = useAuth();
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [activeSection, setActiveSection] = useState<'home' | 'food'>('home');

  console.log('🔴 HomeScreen rendered, logout function:', typeof logout);

  const handleEventsPress = () => {
    console.log('🎯 Attempting to navigate to Events screen...');
    try {
      navigation.navigate('Events');
      console.log('✅ Navigation to Events successful');
    } catch (error) {
      console.error('❌ Navigation to Events failed:', error);
    }
  };

  const handleJobsPress = () => {
    navigation.navigate('Jobs');
  };

  const handleChatPress = () => {
    navigation.navigate('Chat');
  };

  const handleMarketPress = () => {
    alert('שוק קהילתי - בקרוב!');
  };

  const handleFoodPress = () => {
    navigation.navigate('Food');
  };

  const handleAddFoodItem = (item: { title: string; description: string; price: number }) => {
    // TODO: Implement adding food item to backend
    console.log('Adding food item:', item);
  };

  const handleNewsPress = () => {
    alert('חדשות הקהילה - בקרוב!');
  };

  const handlePeoplePress = () => {
    navigation.navigate('People');
  };

  const handleSurveysPress = () => {
    navigation.navigate('Surveys');
  };

  const handleLogout = async () => {
    console.log('🔴 HomeScreen logout button clicked');
    console.log('🔴 HomeScreen logout function type:', typeof logout);
    console.log('🔴 HomeScreen bypassing Alert for testing - calling logout directly');

    try {
      console.log('🔴 HomeScreen calling logout...');
      await logout();
      console.log('🔴 HomeScreen logout completed');
    } catch (error) {
      console.error('🔴 HomeScreen logout error:', error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* App Header */}
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <Icon
            name="home-city"
            iconSet="MaterialCommunityIcons"
            size={24}
            color="#fff"
            style={styles.headerTitleIcon}
          />
          <Text style={styles.headerTitle}>קהילת הכפר</Text>
        </View>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={createHeaderButtonStyle()}
            onPress={() => navigation.navigate('Profile')}
          >
            <Icon
              name={HEADER_ICONS.profile.name}
              iconSet="MaterialIcons"
              size={20}
              color="#fff"
              emoji={HEADER_ICONS.profile.emoji}
              useEmoji={true}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[createHeaderButtonStyle('danger'), { borderWidth: 2, borderColor: 'yellow' }]}
            onPress={handleLogout}
          >
            <Icon
              name={HEADER_ICONS.logout.name}
              iconSet="MaterialIcons"
              size={20}
              color="#fff"
              useEmoji={false}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <WebCompatibleScrollView contentContainerStyle={styles.content}>
        <TextInput
          style={[styles.searchBar, { textAlign: getTextAlign() }]}
          placeholder="🔍 חפש בקהילה..."
          placeholderTextColor="#7f8c8d"
        />

        <FeatureCard iconKey="events" title="אירועי קהילה" subtitle="3 אירועים השבוע" iconColor="#ff6b6b" onPress={handleEventsPress} />
        <FeatureCard iconKey="jobs" title="משרות מקומיות" subtitle="12 משרות חדשות" iconColor="#4ecdc4" onPress={handleJobsPress} />
        <FeatureCard iconKey="people" title="מדריך הכפר" subtitle="רשימת תושבים ופרטי קשר" iconColor="#9b59b6" onPress={handlePeoplePress} />
        <FeatureCard iconKey="chat" title="צ'אט קהילתי" subtitle="5 הודעות שלא נקראו" iconColor="#45b7d1" onPress={handleChatPress} />
        <FeatureCard iconKey="surveys" title="סקרים" subtitle="2 סקרים פתוחים" iconColor="#e67e22" onPress={handleSurveysPress} />
        <FeatureCard iconKey="market" title="שוק קהילתי" subtitle="מוצרים ושירותים מקומיים" iconColor="#f9ca24" onPress={handleMarketPress} />
        <FeatureCard iconKey="food" title="שוק המזון" subtitle="תוצרת טרייה זמינה" iconColor="#27ae60" onPress={handleFoodPress} />
        <FeatureCard iconKey="news" title="חדשות הקהילה" subtitle="2 הודעות חדשות" iconColor="#6c5ce7" onPress={handleNewsPress} />
      </WebCompatibleScrollView>
    </View>
  );
};

type FeatureCardProps = {
  iconKey: keyof typeof FEATURE_ICONS;
  title: string;
  subtitle: string;
  iconColor: string;
  onPress?: () => void;
};

const FeatureCard: React.FC<FeatureCardProps> = ({ iconKey, title, subtitle, iconColor, onPress }) => {
  const { name, emoji } = FEATURE_ICONS[iconKey];

  return (
    <TouchableOpacity style={styles.featureCard} onPress={onPress}>
      <View style={[styles.featureIcon, { backgroundColor: iconColor }]}>
        <Icon
          name={name}
          iconSet="MaterialIcons"
          size={24}
          color="#fff"
          emoji={emoji}
          useEmoji={true}
        />
      </View>
      <View style={styles.featureText}>
        <Text style={[styles.featureTitle, { textAlign: getTextAlign() }]}>{title}</Text>
        <Text style={[styles.featureSubtitle, { textAlign: getTextAlign() }]}>{subtitle}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle(),
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: getColor('primary'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('lg'),
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + getSpacing('lg') : getSpacing('lg'),
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitleIcon: {
    marginRight: getSpacing('sm'),
  },
  headerTitle: {
    color: getColor('white'),
    ...getTypography('lg', 'bold'),
  },
  headerButtons: {
    flexDirection: 'row',
    gap: getSpacing('sm'),
  },
  content: {
    padding: getSpacing('lg'),
  },
  searchBar: {
    backgroundColor: getColor('neutral', 100),
    borderRadius: getBorderRadius('full'),
    padding: getSpacing('md'),
    marginBottom: getSpacing('lg'),
    ...getTypography('sm'),
    textAlign: 'right', // RTL
  },
  featureCard: {
    flexDirection: 'row',
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  featureIcon: {
    width: 50,
    height: 50,
    borderRadius: getBorderRadius('lg'),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getSpacing('lg'),
  },
  featureIconText: {
    ...getTypography('2xl'),
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    ...getTypography('base', 'medium'),
    color: getColor('neutral', 800),
    textAlign: 'right', // RTL
  },
  featureSubtitle: {
    ...getTypography('xs'),
    color: getColor('neutral', 500),
    textAlign: 'right', // RTL
  },
});


