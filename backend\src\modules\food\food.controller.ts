import { Request, Response } from 'express';
import { FoodService } from './food.service';
import { getModuleLogger } from '../../utils/logger';
import { FoodItem } from './food.types';
import { v4 as uuidv4 } from 'uuid';
import { minioStorageClient, BUCKETS } from '../../services/minio-storage.service';

const logger = getModuleLogger('Food');

export class FoodController {
  private foodService: FoodService;

  constructor() {
    this.foodService = new FoodService();
  }

  private transformFoodItemData(data: any, userId: string): Omit<FoodItem, 'id'> {
    return {
      name: data.name,
      description: data.description,
      price: data.price,
      community_id: data.community_id || 'default',
      seller_name: data.sellerName || data.seller_name || 'Anonymous',
      seller_id: userId,
      contact_info: data.contact_info || '',
      pickup_details: data.pickupDetails || null,
      inline_photo_id: data.inlinePhotoId || null,
      data_ai_hint: data.dataAiHint || null,
      created_at: new Date(),
      icon: data.icon,
      icon_set: data.iconSet || data.icon_set,
      image_url: null,
    };
  }

  private toCamelCase(foodItem: FoodItem): any {
    return {
      id: foodItem.id,
      name: foodItem.name,
      description: foodItem.description,
      price: foodItem.price,
      sellerName: foodItem.seller_name,
      sellerId: foodItem.seller_id,
      contactInfo: foodItem.contact_info,
      pickupDetails: foodItem.pickup_details,
      inlinePhotoId: foodItem.inline_photo_id,
      dataAiHint: foodItem.data_ai_hint,
      createdAt: foodItem.created_at,
      communityId: foodItem.community_id,
      icon: foodItem.icon,
      iconSet: foodItem.icon_set,
      imageUrl: foodItem.image_url,
    };
  }

  async getFoodItems(_req: Request, res: Response): Promise<Response> {
    try {
      logger.debug('GET /food/items - Request received');
      const foodItems = await this.foodService.getFoodItems();
      logger.debug('Response body:', { count: foodItems.length });
      return res.json(foodItems.map(this.toCamelCase));
    } catch (error) {
      logger.error('Error fetching food items:', { error });
      return res.status(500).json({ message: 'Error fetching food items', error });
    }
  }

  public createFoodItem = async (req: Request, res: Response) => {
    try {
      logger.info('[FOOD] Creating new food item - Request details:', {
        hasFile: !!req.file,
        fileDetails: req.file ? {
          fieldname: req.file.fieldname,
          originalname: req.file.originalname,
          encoding: req.file.encoding,
          mimetype: req.file.mimetype,
          size: req.file.size,
          buffer: `Buffer(${req.file.buffer.length} bytes)`
        } : null,
        body: req.body,
        headers: {
          'content-type': req.headers['content-type'],
          'content-length': req.headers['content-length']
        }
      });

      const { name, description, price, pickupDetails, contactInfo, icon, iconSet, sellerName } = req.body;
      const foodItemData: Omit<FoodItem, 'id'> = {
        name,
        description,
        price: price.toString(),
        pickup_details: pickupDetails || null,
        contact_info: contactInfo,
        icon: icon || null,
        icon_set: iconSet || null,
        seller_name: sellerName || req.user?.first_name || 'Anonymous',
        seller_id: req.user?.id || 'default', // Get from authenticated user
        community_id: 'default', // TODO: Get from user's community
        inline_photo_id: null,
        data_ai_hint: null,
        created_at: new Date(),
        image_url: null
      };

      let imageFile;
      if (req.file) {
        const fileExtension = req.file.originalname.split('.').pop() || 'jpg';
        const fileName = `${uuidv4()}.${fileExtension}`;
        imageFile = {
          buffer: req.file.buffer,
          filename: fileName,
          mimetype: req.file.mimetype,
        };
        logger.info('[FOOD] Processing image file', {
          originalName: req.file.originalname,
          newFileName: fileName,
          extension: fileExtension,
          mimeType: req.file.mimetype,
          size: req.file.size,
          bufferSize: req.file.buffer.length
        });
      }

      // Upload image to MinIO if provided
      if (imageFile) {
        logger.info('[FOOD] Uploading image before creating food item');
        const imageUrl = await minioStorageClient.uploadImage(
          {
            fieldname: 'image',
            originalname: imageFile.filename,
            encoding: '7bit',
            mimetype: imageFile.mimetype,
            buffer: imageFile.buffer,
            size: imageFile.buffer.length,
            filename: imageFile.filename
          },
          BUCKETS.FOOD_IMAGES
        );
        logger.info('[FOOD] Image uploaded successfully', { image_url: imageUrl });
        foodItemData.image_url = imageUrl;
      }

      const foodItem = await this.foodService.createFoodItem(foodItemData, imageFile);
      logger.info('[FOOD] Food item created successfully', {
        id: foodItem.id,
        name: foodItem.name,
        seller_name: foodItem.seller_name,
        has_image: !!foodItem.image_url,
        image_url: foodItem.image_url
      });

      res.status(201).json(foodItem);
    } catch (error) {
      logger.error('[FOOD] Error creating food item:', error);
      res.status(500).json({ error: 'Failed to create food item' });
    }
  };

  async getFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ message: 'Food item ID is required' });
      }
      logger.debug('GET /food/items/:id - Request received:', { id });
      const foodItem = await this.foodService.getFoodItem(id);
      if (!foodItem) {
        logger.debug('Food item not found:', { id });
        return res.status(404).json({ message: 'Food item not found' });
      }

      // If there's an image, return both data and image in multipart response
      if (foodItem.image_url) {
        try {
          const { data: imageData, contentType } = await minioStorageClient.getImageData(foodItem.image_url, BUCKETS.FOOD_IMAGES);
          
          // Create multipart response
          const boundary = '----WebKitFormBoundary' + Math.random().toString(36).substring(2);
          res.setHeader('Content-Type', `multipart/mixed; boundary=${boundary}`);
          
          // First part: JSON data
          const jsonPart = `--${boundary}\r\n` +
            'Content-Type: application/json\r\n\r\n' +
            JSON.stringify(this.toCamelCase(foodItem)) + '\r\n';
          
          // Second part: Image data
          const imagePart = `--${boundary}\r\n` +
            `Content-Type: ${contentType}\r\n` +
            'Content-Disposition: inline\r\n\r\n';
          
          // Combine all parts
          const responseBuffer = Buffer.concat([
            Buffer.from(jsonPart),
            Buffer.from(imagePart),
            imageData,
            Buffer.from(`\r\n--${boundary}--\r\n`)
          ]);
          
          return res.send(responseBuffer);
        } catch (error) {
          logger.error('Error fetching image data:', error);
          // If image fetch fails, still return the food item data
          return res.json(this.toCamelCase(foodItem));
        }
      }

      // If no image, just return the food item data
      logger.debug('Response body:', foodItem);
      return res.json(this.toCamelCase(foodItem));
    } catch (error) {
      logger.error('Error fetching food item:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error fetching food item', error });
    }
  }

  async updateFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: 'Food item ID is required' });
      }

      // Map camelCase fields to snake_case for update
      const updateData = { ...req.body };
      if (updateData.pickupDetails !== undefined) {
        updateData.pickup_details = updateData.pickupDetails;
        delete updateData.pickupDetails;
      }
      if (updateData.inlinePhotoId !== undefined) {
        updateData.inline_photo_id = updateData.inlinePhotoId;
        delete updateData.inlinePhotoId;
      }
      if (updateData.dataAiHint !== undefined) {
        updateData.data_ai_hint = updateData.dataAiHint;
        delete updateData.dataAiHint;
      }

      let imageFile;
      if (req.file) {
        const fileExtension = req.file.originalname.split('.').pop() || 'jpg';
        const fileName = `${uuidv4()}.${fileExtension}`;
        imageFile = {
          buffer: req.file.buffer,
          filename: fileName,
          mimetype: req.file.mimetype,
        };
        logger.info('[FOOD] Processing image file', {
          originalName: req.file.originalname,
          newFileName: fileName,
          extension: fileExtension,
          mimeType: req.file.mimetype,
          size: req.file.size,
          bufferSize: req.file.buffer.length
        });
      }

      const foodItem = await this.foodService.updateFoodItem(id, updateData, imageFile);
      if (!foodItem) {
        return res.status(404).json({ message: 'Food item not found' });
      }

      return res.json(this.toCamelCase(foodItem));
    } catch (error) {
      console.error('Error updating food item:', error);
      return res.status(500).json({ error: 'Failed to update food item' });
    }
  }

  async deleteFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: 'Food item ID is required' });
      }

      const deleted = await this.foodService.deleteFoodItem(id);
      if (!deleted) {
        return res.status(404).json({ error: 'Food item not found' });
      }

      return res.status(204).send();
    } catch (error) {
      console.error('Error deleting food item:', error);
      return res.status(500).json({ error: 'Failed to delete food item' });
    }
  }
} 