import { Router } from 'express';
import { FoodController } from './food.controller';
import { upload, handleMulterError } from '../../middleware/upload.middleware';
import { getAuthMiddleware } from '../../middleware/authSelector';

const router = Router();
const foodController = new FoodController();
const authMiddleware = getAuthMiddleware();

// Public routes
router.get('/', authMiddleware, foodController.getFoodItems.bind(foodController));
router.get('/:id', authMiddleware, foodController.getFoodItem.bind(foodController));

// Protected routes with file upload handling
router.post('/', 
  authMiddleware,
  upload.single('image'),
  handleMulterError,
  foodController.createFoodItem.bind(foodController)
);

router.put('/:id', 
  authMiddleware,
  upload.single('image'),
  handleMulterError,
  foodController.updateFoodItem.bind(foodController)
);

router.delete('/:id', authMiddleware, foodController.deleteFoodItem.bind(foodController));

export default router; 