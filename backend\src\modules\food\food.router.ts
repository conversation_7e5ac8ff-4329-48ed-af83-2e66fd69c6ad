import { Router } from 'express';
import { FoodController } from './food.controller';
import { upload, handleMulterError } from '../../middleware/upload.middleware';

const router = Router();
const foodController = new FoodController();

// Public routes
router.get('/', foodController.getFoodItems.bind(foodController));
router.get('/:id', foodController.getFoodItem.bind(foodController));

// Protected routes with file upload handling
router.post('/', 
  upload.single('image'),
  handleMulterError,
  foodController.createFoodItem.bind(foodController)
);

router.put('/:id', 
  upload.single('image'),
  handleMulterError,
  foodController.updateFoodItem.bind(foodController)
);

router.delete('/:id', foodController.deleteFoodItem.bind(foodController));

export default router; 