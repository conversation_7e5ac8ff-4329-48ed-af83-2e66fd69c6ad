import request from 'supertest';
import { app } from '../../../app';
import { SupabaseAuthWrapper } from '../../../__tests__/supabaseAuth';
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

describe('Survey API', () => {
  let auth: SupabaseAuthWrapper;
  let authToken: string;

  beforeEach(async () => {
    auth = new SupabaseAuthWrapper();
    const { token } = await auth.signUp();
    authToken = token;
  });

  afterEach(async () => {
    await auth.signOut();
  });

  describe('Authentication', () => {
    it('should require valid Supabase token for protected routes', async () => {
      const surveyData = {
        title: 'Test Survey',
        description: 'Test Description',
        questions: [
          {
            text: 'What is your favorite color?',
            type: 'single-choice',
            options: ['Red', 'Blue', 'Green']
          }
        ],
        status: 'DRAFT',
        communityId: 'test-community',
        closingDate: new Date().toISOString()
      };

      // Try to create survey without token
      const response = await request(app)
        .post('/api/surveys')
        .send(surveyData);

      expect(response.status).toBe(401);
    });
  });
}); 