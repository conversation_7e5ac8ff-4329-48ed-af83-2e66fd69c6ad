<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <title>
   Local Market - Village Community App
  </title>
  <style>
   * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 350px;
            position: relative;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .header-action {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .header-action:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        .search-bar {
            background: #f8f9fa;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            width: 100%;
            margin-bottom: 20px;
            font-size: 14px;
            outline: none;
            transition: box-shadow 0.2s;
        }
        
        .search-bar:focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }
        
        .market-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }
        
        .market-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            flex-shrink: 0;
            background: #f9ca24;
        }
        
        .card-text {
            flex: 1;
        }
        
        .card-text h3 {
            font-size: 16px;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .card-text p {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }
        
        .price-tag {
            background: #27ae60;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: transform 0.2s;
            border: none;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            padding: 10px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #7f8c8d;
        }
        
        .nav-item:hover {
            transform: scale(1.1);
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Add modal styles for selling item */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal {
            background: white;
            border-radius: 15px;
            padding: 30px;
            width: 90%;
            max-width: 300px;
            text-align: center;
        }
        
        .modal h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .modal p {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .modal-buttons {
            display: flex;
            gap: 10px;
        }
        
        .modal-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.2s;
        }
        
        .modal-button.primary {
            background: #667eea;
            color: white;
        }
        
        .modal-button.primary:hover {
            background: #5a67d8;
        }
        
        .modal-button.secondary {
            background: #f8f9fa;
            color: #2c3e50;
        }
        
        .modal-button.secondary:hover {
            background: #e9ecef;
        }
        
        /* Category filter */
        .category-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .category-filter {
            background: #f8f9fa;
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .category-filter.active {
            background: #667eea;
            color: white;
        }
        
        .category-filter:hover:not(.active) {
            background: #e9ecef;
        }
  </style>
 </head>
 <body>
  <div class="container">
   <h1>
    🛒 Local Market Screens
   </h1>
   <div class="mockups-grid">
    <div class="phone-mockup">
     <div class="screen-label">
      MARKET
     </div>
     <div class="screen">
      <div class="status-bar">
       <span>
        9:41
       </span>
       <span>
        🔋 100%
       </span>
      </div>
      <div class="app-header">
       <div class="header-title">
        🛒 Local Market
       </div>
       <button class="header-action" onclick="showSellModal()">
        Sell
       </button>
      </div>
      <div class="content">
       <input class="search-bar" oninput="filterProducts(this.value)" placeholder="🔍 Search products..." type="text"/>
       <div class="category-filters">
        <button class="category-filter active" onclick="filterByCategory('all')">
         All
        </button>
        <button class="category-filter" onclick="filterByCategory('food')">
         Food
        </button>
        <button class="category-filter" onclick="filterByCategory('electronics')">
         Electronics
        </button>
        <button class="category-filter" onclick="filterByCategory('books')">
         Books
        </button>
        <button class="category-filter" onclick="filterByCategory('furniture')">
         Furniture
        </button>
        <button class="category-filter" onclick="filterByCategory('clothing')">
         Clothing
        </button>
        <button class="category-filter" onclick="filterByCategory('other')">
         Other
        </button>
       </div>
       <div id="market-items">
        <div class="market-card" data-category="food" data-keywords="tomatoes organic vegetables fresh garden">
         <div class="card-icon">
          🍅
         </div>
         <div class="card-text">
          <h3>
           Fresh Tomatoes
          </h3>
          <p>
           Organic tomatoes from Maria's garden. Perfect for cooking!
          </p>
         </div>
         <div class="price-tag">
          $3/lb
         </div>
        </div>
        <div class="market-card" data-category="electronics" data-keywords="bicycle bike kids mountain sports">
         <div class="card-icon">
          🚲
         </div>
         <div class="card-text">
          <h3>
           Kids Bicycle
          </h3>
          <p>
           Great condition, blue mountain bike. Suitable for ages 8-12.
          </p>
         </div>
         <div class="price-tag">
          $75
         </div>
        </div>
        <div class="market-card" data-category="books" data-keywords="textbooks school education math science">
         <div class="card-icon">
          📚
         </div>
         <div class="card-text">
          <h3>
           School Textbooks
          </h3>
          <p>
           High school math and science books. Previous year edition.
          </p>
         </div>
         <div class="price-tag">
          $25/set
         </div>
        </div>
        <div class="market-card" data-category="furniture" data-keywords="table dining chairs wooden furniture">
         <div class="card-icon">
          🪑
         </div>
         <div class="card-text">
          <h3>
           Dining Table Set
          </h3>
          <p>
           Wooden table with 4 chairs. Moving sale, must go this week.
          </p>
         </div>
         <div class="price-tag">
          $150
         </div>
        </div>
        <div class="market-card" data-category="food" data-keywords="honey local natural sweet beehive">
         <div class="card-icon">
          🍯
         </div>
         <div class="card-text">
          <h3>
           Local Honey
          </h3>
          <p>
           Pure honey from village beehives. Various sizes available.
          </p>
         </div>
         <div class="price-tag">
          $8/jar
         </div>
        </div>
        <div class="market-card" data-category="clothing" data-keywords="kids clothing children clothes shirts pants">
         <div class="card-icon">
          👕
         </div>
         <div class="card-text">
          <h3>
           Kids Clothing
          </h3>
          <p>
           Gently used children's clothes, sizes 4-8. Clean and ready to wear.
          </p>
         </div>
         <div class="price-tag">
          $2/item
         </div>
        </div>
        <div class="market-card" data-category="electronics" data-keywords="laptop computer desk work office">
         <div class="card-icon">
          💻
         </div>
         <div class="card-text">
          <h3>
           Laptop Computer
          </h3>
          <p>
           Used laptop in good condition. Perfect for students or light work.
          </p>
         </div>
         <div class="price-tag">
          $200
         </div>
        </div>
        <div class="market-card" data-category="other" data-keywords="guitar music instrument acoustic learn">
         <div class="card-icon">
          🎸
         </div>
         <div class="card-text">
          <h3>
           Acoustic Guitar
          </h3>
          <p>
           Beginner-friendly acoustic guitar with carrying case included.
          </p>
         </div>
         <div class="price-tag">
          $90
         </div>
        </div>
        <div class="market-card" data-category="food" data-keywords="bread bakery fresh homemade sourdough">
         <div class="card-icon">
          🍞
         </div>
         <div class="card-text">
          <h3>
           Homemade Bread
          </h3>
          <p>
           Fresh sourdough bread baked daily. Order ahead for weekend pickup.
          </p>
         </div>
         <div class="price-tag">
          $5/loaf
         </div>
        </div>
        <div class="market-card" data-category="other" data-keywords="plants garden herbs indoor outdoor">
         <div class="card-icon">
          🪴
         </div>
         <div class="card-text">
          <h3>
           Indoor Plants
          </h3>
          <p>
           Various houseplants and herbs. Great for beginners, low maintenance.
          </p>
         </div>
         <div class="price-tag">
          $12/each
         </div>
        </div>
       </div>
      </div>
      <div class="bottom-nav">
       <div class="nav-item" onclick="navigateTo('home')">
        <div>
         🏠
        </div>
        <div class="nav-label">
         Home
        </div>
       </div>
       <div class="nav-item" onclick="navigateTo('people')">
        <div>
         👥
        </div>
        <div class="nav-label">
         People
        </div>
       </div>
       <div class="nav-item" onclick="navigateTo('chat')">
        <div>
         💬
        </div>
        <div class="nav-label">
         Chat
        </div>
       </div>
       <div class="nav-item active" onclick="navigateTo('market')">
        <div>
         🛒
        </div>
        <div class="nav-label">
         Market
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="screen-label">
     SELL PAGE
    </div>
    <div class="phone-mockup">
     <div class="screen">
      <div class="status-bar">
       <span>
        9:41
       </span>
       <span>
        🔋 100%
       </span>
      </div>
      <div class="app-header">
       <div class="header-title">
        📤 Sell Page
       </div>
       <button class="header-action" onclick="showSellModal()">
        Sell
       </button>
      </div>
      <div class="content">
       <div class="form-group">
        <input class="search-bar" placeholder="Item Name" type="text" value="Homemade Jam"/>
       </div>
       <div class="form-group">
        <input class="search-bar" placeholder="Short Description" type="text" value="Organic strawberry jam in glass jars"/>
       </div>
       <div class="form-group">
        <input class="search-bar" placeholder="Price (e.g. $10)" type="text" value="$6/jar"/>
       </div>
       <div class="form-group">
        <select class="search-bar">
         <option selected="">
          Food
         </option>
         <option>
          Electronics
         </option>
         <option>
          Books
         </option>
         <option>
          Furniture
         </option>
         <option>
          Clothing
         </option>
         <option>
          Other
         </option>
        </select>
       </div>
       <div class="form-group">
        <textarea class="search-bar" style="height:100px;">Delicious jam made from local strawberries. No added sugar. Limited supply!</textarea>
       </div>
       <button class="modal-button primary" style="margin-top: 10px;">
        Post Listing
       </button>
      </div>
      <div class="bottom-nav">
       <div class="nav-item" onclick="navigateTo('home')">
        <div>
         🏠
        </div>
        <div class="nav-label">
         Home
        </div>
       </div>
       <div class="nav-item" onclick="navigateTo('people')">
        <div>
         👥
        </div>
        <div class="nav-label">
         People
        </div>
       </div>
       <div class="nav-item" onclick="navigateTo('chat')">
        <div>
         💬
        </div>
        <div class="nav-label">
         Chat
        </div>
       </div>
       <div class="nav-item active" onclick="navigateTo('market')">
        <div>
         🛒
        </div>
        <div class="nav-label">
         Market
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
