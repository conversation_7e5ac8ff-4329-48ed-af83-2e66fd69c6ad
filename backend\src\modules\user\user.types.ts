import { User, UserRole, UserType } from '../../generated/prisma';

export interface UIPreferences {
  theme: 'light' | 'dark' | 'auto';
  colorScheme: 'default' | 'blue' | 'green' | 'purple' | 'orange';
  fontSize: 'small' | 'medium' | 'large' | 'extraLarge';
  spacing: 'compact' | 'medium' | 'comfortable';
  highContrast: boolean;
}

export interface CreateUserDTO {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  userType: UserType;
  phone?: string;
  language?: string;
  uiPreferences?: UIPreferences;
}

export interface CreateUserProfileDTO {
  id: string; // Supabase user ID
  email: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  userType: UserType;
  phone?: string;
  language?: string;
  uiPreferences?: UIPreferences;
}

export interface UpdateUserDTO {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  userType?: UserType;
  phone?: string;
  language?: string;
  isActive?: boolean;
  uiPreferences?: UIPreferences;
}

export interface UpdateUIPreferencesDTO {
  uiPreferences: UIPreferences;
}

export interface UserLoginDTO {
  email: string;
  password: string;
}

// Type to match Prisma User model
export type UserResponse = {
  id: string;
  community_id: string;
  email: string;
  password: string | null;
  role: UserRole;
  user_type: UserType;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  language: string;
  is_active: boolean;
  last_login: Date | null;
  google_id: string | null;
  ui_preferences: any | null; // JSON field for UI preferences
  created_at: Date | null;
  updated_at: Date;
};