_format_version: "2.1"
_transform: true

services:
  - name: auth-service
    url: http://auth:9999
    routes:
      - name: auth-route
        paths:
          - /auth/v1
        strip_path: true

  - name: rest-service
    url: http://rest:3000
    routes:
      - name: rest-route
        paths:
          - /rest
        strip_path: true

  - name: storage-service
    url: http://storage:5000
    routes:
      - name: storage-route
        paths:
          - /storage
        strip_path: true

  - name: realtime-service
    url: http://realtime:4000
    routes:
      - name: realtime-route
        paths:
          - /realtime
        strip_path: true

plugins:
  - name: cors
    config:
      origins: ['*']
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
      headers: [
        'Accept', 'Accept-Version', 'Content-Length', 'Content-MD5', 'Content-Type', 'Date', 'Authorization',
        'apikey', 'X-Client-Info', 'x-supabase-api-version', 'x-supabase-client', 'x-supabase-auth',
        'x-supabase-anon-key', 'x-supabase-service-role-key', 'x-supabase-access-token'
      ]
      exposed_headers: ['Content-Length', 'Content-Range']
      credentials: true
      max_age: 3600 