import multer from 'multer';
import { Request, Response, NextFunction } from 'express';

// Get max file size from environment variable (default to 5MB if not set)
const MAX_FILE_SIZE = parseInt(process.env['MAX_FILE_SIZE'] || '5242880', 10);

// Configure storage to use memory
const storage = multer.memoryStorage();

// File filter
const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

// Create multer instance
export const upload = multer({
  storage: storage,
  limits: {
    fileSize: MAX_FILE_SIZE, // Use the value from environment variable
  },
  fileFilter: fileFilter
});

// Error handling middleware for multer
export const handleMulterError = (err: Error, _req: Request, res: Response, next: NextFunction): void => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      const maxSizeMB = MAX_FILE_SIZE / (1024 * 1024);
      res.status(400).json({
        error: `File size too large. Maximum size is ${maxSizeMB}MB`
      });
      return;
    }
    res.status(400).json({
      error: err.message
    });
    return;
  }
  if (err) {
    res.status(400).json({
      error: err.message
    });
    return;
  }
  next();
}; 