import React from 'react';
import { Text, StyleSheet, TextStyle } from 'react-native';
import { getTypography, getColor } from '../../theme';

interface TypographyProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: 'primary' | 'secondary' | 'text' | 'textSecondary' | 'error' | 'success' | 'warning' | 'info';
  align?: 'left' | 'center' | 'right' | 'justify';
  style?: TextStyle;
}

const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body1',
  color = 'text',
  align = 'left',
  style,
}) => {
  const getVariantStyle = () => {
    switch (variant) {
      case 'h1':
        return getTypography('5xl', 'bold', '5xl');
      case 'h2':
        return getTypography('4xl', 'bold', '4xl');
      case 'h3':
        return getTypography('3xl', 'bold', '3xl');
      case 'h4':
        return getTypography('2xl', 'bold', '2xl');
      case 'h5':
        return getTypography('xl', 'bold', 'xl');
      case 'h6':
        return getTypography('lg', 'bold', 'lg');
      case 'body1':
        return getTypography('base', 'normal', 'base');
      case 'body2':
        return getTypography('sm', 'normal', 'sm');
      case 'caption':
        return getTypography('xs', 'normal', 'xs');
      case 'overline':
        return getTypography('xs', 'medium', 'xs');
      default:
        return getTypography('base', 'normal', 'base');
    }
  };

  const getColorValue = () => {
    switch (color) {
      case 'primary':
        return getColor('primary');
      case 'secondary':
        return getColor('secondary');
      case 'text':
        return getColor('neutral', 900);
      case 'textSecondary':
        return getColor('neutral', 600);
      case 'error':
        return getColor('error');
      case 'success':
        return getColor('success');
      case 'warning':
        return getColor('warning');
      case 'info':
        return getColor('info');
      default:
        return getColor('neutral', 900);
    }
  };

  return (
    <Text
      style={[
        getVariantStyle(),
        { color: getColorValue(), textAlign: align },
        style,
      ]}
    >
      {children}
    </Text>
  );
};

export default Typography;
