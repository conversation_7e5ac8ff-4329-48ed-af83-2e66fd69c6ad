import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { Survey, SurveyQuestion, surveysService } from '@services/surveys';

type SurveyResultsScreenRouteProp = RouteProp<SurveysStackParamList, 'SurveyResults'>;
type SurveyResultsScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'SurveyResults'>;

export default function AnswerSurveyScreen() {
  const { t } = useTranslation();
  const route = useRoute<SurveyResultsScreenRouteProp>();
  const navigation = useNavigation<SurveyResultsScreenNavigationProp>();
  const { surveyId } = route.params;

  const [survey, setSurvey] = useState<Survey | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [answers, setAnswers] = useState<{ [questionId: string]: any }>({});

  useEffect(() => {
    loadSurvey();
  }, [surveyId]);

  const loadSurvey = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading survey for answering:', surveyId);
      const surveyData = await surveysService.getSurveyById(surveyId);

      if (surveyData) {
        setSurvey(surveyData);
        console.log('✅ Survey loaded for answering:', surveyData.title);
      } else {
        Alert.alert('שגיאה', 'הסקר לא נמצא');
      }
    } catch (error) {
      console.error('❌ Error loading survey:', error);
      Alert.alert('שגיאה', 'שגיאה בטעינת הסקר');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmit = async () => {
    if (!survey) return;

    // Validate required questions
    const requiredQuestions = survey.questions.filter(q => q.required);
    const missingAnswers = requiredQuestions.filter(q => !answers[q.id] || answers[q.id] === '');

    if (missingAnswers.length > 0) {
      Alert.alert('שגיאה', 'אנא ענו על כל השאלות החובה');
      return;
    }

    setSubmitting(true);
    try {
      // TODO: Implement survey response submission
      console.log('📝 Submitting survey answers:', answers);

      Alert.alert(
        'תודה!',
        'התשובות שלכם נשלחו בהצלחה',
        [{ text: 'אישור', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('❌ Error submitting survey:', error);
      Alert.alert('שגיאה', 'אירעה שגיאה בשליחת התשובות');
    } finally {
      setSubmitting(false);
    }
  };

  const renderQuestion = (question: SurveyQuestion, index: number) => {
    switch (question.type) {
      case 'single-choice':
        return (
          <View key={question.id} style={styles.questionContainer}>
            <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
              {question.text}
            </Text>
            {question.options?.map((option, optionIndex) => (
              <TouchableOpacity
                key={optionIndex}
                style={styles.optionContainer}
                onPress={() => handleAnswerChange(question.id, option)}
              >
                <View style={styles.radioButton}>
                  {answers[question.id] === option && (
                    <View style={styles.radioButtonSelected} />
                  )}
                </View>
                <Text style={[styles.optionText, { textAlign: getTextAlign() }]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'multiple-choice':
        return (
          <View key={question.id} style={styles.questionContainer}>
            <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
              {question.text}
            </Text>
            {question.options?.map((option, optionIndex) => {
              const selectedOptions = answers[question.id] || [];
              const isSelected = selectedOptions.includes(option);

              return (
                <TouchableOpacity
                  key={optionIndex}
                  style={styles.optionContainer}
                  onPress={() => {
                    const currentAnswers = answers[question.id] || [];
                    let newAnswers;
                    if (isSelected) {
                      newAnswers = currentAnswers.filter((a: string) => a !== option);
                    } else {
                      newAnswers = [...currentAnswers, option];
                    }
                    handleAnswerChange(question.id, newAnswers);
                  }}
                >
                  <View style={styles.checkbox}>
                    {isSelected && (
                      <Text style={styles.checkmark}>✓</Text>
                    )}
                  </View>
                  <Text style={[styles.optionText, { textAlign: getTextAlign() }]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        );

      case 'text':
        return (
          <View key={question.id} style={styles.questionContainer}>
            <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
              {question.text}
            </Text>
            <TextInput
              style={[styles.textInput, { textAlign: getTextAlign() }]}
              placeholder="הכניסו את תשובתכם כאן..."
              value={answers[question.id] || ''}
              onChangeText={(text) => handleAnswerChange(question.id, text)}
              multiline
              numberOfLines={3}
            />
          </View>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען סקר...</Text>
      </View>
    );
  }

  if (!survey) {
    return (
      <View style={styles.centerContainer}>
        <Text>הסקר לא נמצא</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="📋 ענו על הסקר"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <Text style={[styles.surveyTitle, { textAlign: getTextAlign() }]}>
          {survey.title}
        </Text>

        {survey.questions.map((question, index) => renderQuestion(question, index))}

        {/* Additional Comments */}
        <View style={styles.questionContainer}>
          <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
            הערות נוספות
          </Text>
          <TextInput
            style={[styles.textInput, { textAlign: getTextAlign() }]}
            placeholder="הערות נוספות (אופציונלי)..."
            value={answers['comments'] || ''}
            onChangeText={(text) => handleAnswerChange('comments', text)}
            multiline
            numberOfLines={3}
          />
        </View>

        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={submitting}
        >
          <Text style={[styles.submitButtonText, { textAlign: getTextAlign() }]}>
            {submitting ? 'שולח תשובות...' : 'שלח תשובות'}
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle({
    flex: 1,
    backgroundColor: getColor('background'),
  }),
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: getColor('background'),
  },
  content: {
    flex: 1,
  },
  scrollContent: createWebCompatibleScrollContentStyle({
    flexGrow: 1,
    padding: getSpacing('md'),
  }),
  surveyTitle: {
    ...getTypography('headingLarge'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('xl'),
  },
  questionContainer: {
    backgroundColor: getColor('surface'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    marginBottom: getSpacing('lg'),
    ...getShadow('xs'),
  },
  questionText: {
    ...getTypography('headingSmall'),
    color: getColor('onSurface'),
    marginBottom: getSpacing('md'),
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('sm'),
    paddingVertical: getSpacing('xs'),
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: getColor('primary'),
    marginRight: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: getColor('primary'),
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: getColor('primary'),
    marginRight: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 3,
  },
  checkmark: {
    color: getColor('primary'),
    fontSize: 14,
    fontWeight: 'bold',
  },
  optionText: {
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    flex: 1,
  },
  textInput: {
    backgroundColor: getColor('background'),
    borderWidth: 1,
    borderColor: getColor('outline'),
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('md'),
    ...getTypography('bodyMedium'),
    color: getColor('onSurface'),
    minHeight: 80,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
    alignItems: 'center',
    marginTop: getSpacing('xl'),
    ...getShadow('sm'),
  },
  submitButtonText: {
    ...getTypography('labelLarge'),
    color: '#fff',
    fontWeight: 'bold',
  },
});
