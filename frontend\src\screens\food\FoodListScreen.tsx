import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Image,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import SearchBarWithBack from '@components/SearchBarWithBack';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { HEADER_ICONS } from '@constants';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { FoodStackParamList } from '@navigation/types';
import { FoodItem } from '../../types/food';
import { apiService } from '../../services/api';

type FoodListScreenNavigationProp = StackNavigationProp<FoodStackParamList, 'FoodList'>;

function isEmoji(str: string | null | undefined) {
  // Simple check: if it's a single character and matches emoji unicode range
  return !!str && str.length <= 2 && /[\u231A-\uD83E\uDDFF]/.test(str);
}

export default function FoodListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<FoodListScreenNavigationProp>();
  const [foodItems, setFoodItems] = useState<FoodItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const loadFoodItems = async () => {
    try {
      const response = await apiService.getFoodItems();
      if (response.data) {
        setFoodItems(response.data);
      }
    } catch (error) {
      console.error('Error loading food items:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadFoodItems();
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadFoodItems();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadFoodItems();
  };

  const handleAddItem = () => {
    navigation.navigate('CreateFood');
  };

  const handleItemPress = (itemId: string) => {
    navigation.navigate('FoodDetails', { foodId: itemId });
  };

  const filteredItems = foodItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderFoodItem = ({ item }: { item: FoodItem }) => (
    <TouchableOpacity
      style={styles.foodCard}
      onPress={() => handleItemPress(item.id)}
    >
      {item.image && (
        <View style={styles.foodImageContainer}>
          <Image
            source={{ uri: item.image.startsWith('data:image/') ? item.image : `data:image/jpeg;base64,${item.image}` }}
            style={styles.foodImage}
            resizeMode="cover"
            onError={(error) => console.error('[FoodListScreen] Image loading error:', error.nativeEvent.error)}
          />
        </View>
      )}
      <View style={styles.foodIconContainer}>
        {isEmoji(item.icon) ? (
          <Text style={styles.foodIcon}>{item.icon || '🍲'}</Text>
        ) : (
          <Icon
            name={item.icon || 'food'}
            iconSet={item.iconSet || 'MaterialCommunityIcons'}
            size={24}
            color="#fff"
          />
        )}
      </View>

      <View style={styles.cardContent}>
        <Text style={styles.foodTitle}>{item.name}</Text>
        <Text style={styles.foodDescription}>{item.description}</Text>

        <View style={styles.metaRow}>
          <Text style={styles.metaIcon}>👤</Text>
          <Text style={styles.metaText}>{item.sellerName}</Text>
          <Text style={styles.metaIcon}>📍</Text>
          <Text style={styles.metaText}>{item.pickupDetails}</Text>
        </View>
      </View>

      <View style={styles.priceTag}>
        <Text style={styles.priceText}>₪{item.price}</Text>
      </View>
    </TouchableOpacity>
  );

  const addButton = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleAddItem}
    >
      <Text style={styles.addButtonText}>+ הוסף</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען מוצרי מזון...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="🍲 מוצרי אוכל"
        showBackButton={true}
        rightComponent={addButton}
      />

      <SearchBarWithBack
        placeholder="חיפוש מוצרים..."
        value={searchQuery}
        onChangeText={setSearchQuery}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <FlatList
          data={filteredItems}
          renderItem={renderFoodItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      </WebCompatibleScrollView>

      <TouchableOpacity
        style={styles.fab}
        onPress={handleAddItem}
        activeOpacity={0.8}
      >
        <Icon name="plus" iconSet="MaterialCommunityIcons" size={32} color={getColor('white')} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: getSpacing('lg'),
  },
  foodCard: {
    flexDirection: 'row',
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  foodIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 12,
    marginRight: getSpacing('md'),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9ca24',
  },
  foodIcon: {
    fontSize: 24,
  },
  cardContent: {
    flex: 1,
  },
  foodTitle: {
    ...getTypography('lg', 'bold'),
    color: getColor('primary'),
    marginBottom: getSpacing('xs'),
  },
  foodDescription: {
    ...getTypography('sm'),
    color: getColor('neutral', 600),
    marginBottom: getSpacing('sm'),
  },
  metaRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    gap: getSpacing('xs'),
    marginTop: getSpacing('sm'),
    direction: 'rtl',
  },
  metaText: {
    ...getTypography('xs'),
    color: getColor('neutral', 400),
    textAlign: 'right',
    marginLeft: getSpacing('xs'),
    direction: 'rtl',
  },
  metaIcon: {
    fontSize: 14,
    marginLeft: getSpacing('xs'),
    textAlign: 'right',
    direction: 'rtl',
  },
  priceTag: {
    backgroundColor: '#27ae60',
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('xs'),
    marginLeft: getSpacing('md'),
  },
  priceText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  fab: {
    position: 'absolute',
    bottom: getSpacing('lg'),
    right: getSpacing('lg'),
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('full'),
    padding: getSpacing('lg'),
    ...getShadow('md'),
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('sm'),
    borderRadius: getBorderRadius('full'),
  },
  addButtonText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  foodImageContainer: {
    width: '100%',
    height: 150,
    marginBottom: getSpacing('md'),
    borderRadius: getBorderRadius('lg'),
    overflow: 'hidden',
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
}); 