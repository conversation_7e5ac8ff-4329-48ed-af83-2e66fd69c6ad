import React from 'react';
import { StyleProp, ViewStyle, Text } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';

export type IconSet = 'MaterialIcons' | 'MaterialCommunityIcons' | 'FontAwesome' | 'Ionicons';

interface IconProps {
  name: string;
  size?: number;
  color?: string;
  iconSet?: IconSet;
  style?: StyleProp<ViewStyle>;
  emoji?: string; // New prop for emoji display
  useEmoji?: boolean; // New prop to control emoji vs vector display
}

const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = '#000',
  iconSet = 'MaterialIcons',
  style,
  emoji,
  useEmoji = true, // Default to using emoji when available
}) => {
  // If emoji is provided and useEmoji is true, display emoji
  if (emoji && useEmoji) {
    return (
      <Text style={[{ fontSize: size, color }, style]}>
        {emoji}
      </Text>
    );
  }

  // Otherwise, use vector icon
  const getIconComponent = () => {
    switch (iconSet) {
      case 'MaterialCommunityIcons':
        return MaterialCommunityIcons;
      case 'FontAwesome':
        return FontAwesome;
      case 'Ionicons':
        return Ionicons;
      default:
        return MaterialIcons;
    }
  };

  const IconComponent = getIconComponent();

  return (
    <IconComponent
      name={name}
      size={size}
      color={color}
      style={style}
    />
  );
};

export default Icon;
