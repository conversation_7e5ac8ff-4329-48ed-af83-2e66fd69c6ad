<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Village Community App - Chat Conversations</title>
<style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 350px;
            margin: 0 auto;
            position: relative;
        }
        
        .screen-label {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .chat-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }
        
        .chat-info {
            flex: 1;
        }
        
        .chat-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 2px;
        }
        
        .chat-status {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .chat-actions {
            display: flex;
            gap: 15px;
        }
        
        .chat-action {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .message {
            max-width: 80%;
            display: flex;
            flex-direction: column;
        }
        
        .message.sent {
            align-self: flex-end;
            align-items: flex-end;
        }
        
        .message.received {
            align-self: flex-start;
            align-items: flex-start;
        }
        
        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
            position: relative;
        }
        
        .message.sent .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 6px;
        }
        
        .message.received .message-bubble {
            background: white;
            color: #2c3e50;
            border: 1px solid #e1e8ed;
            border-bottom-left-radius: 6px;
        }
        
        .message-time {
            font-size: 11px;
            color: #95a5a6;
            margin-top: 4px;
        }
        
        .message-input-container {
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #e1e8ed;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 14px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: #667eea;
        }
        
        .send-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #95a5a6;
            font-size: 12px;
            font-style: italic;
            margin-bottom: 10px;
        }
        
        .typing-dots {
            display: flex;
            gap: 2px;
        }
        
        .typing-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #95a5a6;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% { opacity: 0.3; }
            30% { opacity: 1; }
        }
        
        .system-message {
            text-align: center;
            font-size: 12px;
            color: #95a5a6;
            margin: 10px 0;
            padding: 8px 16px;
            background: rgba(149, 165, 166, 0.1);
            border-radius: 12px;
            align-self: center;
            max-width: none;
        }
        
        .group-avatar {
            font-size: 20px;
        }
        
        .emergency-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .announcement-header {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        
        .quick-replies {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }
        
        .quick-reply {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid #667eea;
            border-radius: 15px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-reply:hover {
            background: #667eea;
            color: white;
        }

        /* New styles for group creation */
        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            border-color: #667eea;
        }

        .form-textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 14px;
            outline: none;
            resize: none;
            height: 80px;
            transition: border-color 0.2s;
        }

        .form-textarea:focus {
            border-color: #667eea;
        }

        .group-type-selector {
            display: flex;
            gap: 10px;
        }

        .group-type-option {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .group-type-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .group-type-option .icon {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .group-type-option .label {
            font-size: 12px;
            font-weight: 600;
            color: #95a5a6;
        }

        .group-type-option.selected .label {
            color: #667eea;
        }

        .tip-box {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .tip-title {
            font-size: 12px;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .tip-content {
            font-size: 12px;
            color: #2c3e50;
            line-height: 1.4;
        }

        .photo-upload {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
            cursor: pointer;
            border: 3px dashed rgba(102, 126, 234, 0.3);
        }

        .photo-upload-label {
            color: #667eea;
            font-size: 14px;
            text-align: center;
        }

        /* Member selection styles */
        .search-container {
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #e1e8ed;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .search-icon {
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            color: #95a5a6;
        }

        .selected-members {
            padding: 15px 20px;
            background: #f8f9ff;
            border-bottom: 1px solid #e1e8ed;
        }

        .selected-title {
            font-size: 12px;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .selected-chips {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .selected-chip {
            display: flex;
            align-items: center;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .chip-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .chip-remove {
            background: none;
            border: none;
            margin-left: 8px;
            color: #95a5a6;
            cursor: pointer;
        }

        .contacts-list {
            flex: 1;
            overflow-y: auto;
        }

        .contacts-section {
            padding: 15px 20px;
        }

        .contacts-title {
            font-size: 12px;
            color: #95a5a6;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
        }

        .contact-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .contact-info {
            flex: 1;
        }

        .contact-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .contact-status {
            font-size: 12px;
            color: #95a5a6;
        }

        .contact-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .contact-checkbox.selected {
            border-color: #667eea;
        }

        .checkbox-inner {
            width: 10px;
            height: 10px;
            background: #667eea;
            border-radius: 2px;
        }
    
/* Added CHAT LIST styles */
.chat-list {
    height: calc(100% - 60px);
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.chat-content {
    flex: 1;
}

.chat-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #2c3e50;
}

.chat-preview {
    font-size: 12px;
    color: #7f8c8d;
}

.chat-time {
    font-size: 11px;
    color: #bdc3c7;
}

.unread-badge {
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    margin-left: 8px;
}

.search-bar {
    background: #f8f9fa;
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    width: 100%;
    margin-bottom: 20px;
    font-size: 14px;
}

/* Full CHAT screen styling */

.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title {
    font-weight: bold;
    font-size: 18px;
}

.header-action {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
}

.search-bar {
    background: #f8f9fa;
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    width: 100%;
    margin-bottom: 20px;
    font-size: 14px;
}

.chat-list {
    height: calc(100% - 60px);
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.chat-content {
    flex: 1;
}

.chat-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #2c3e50;
}

.chat-preview {
    font-size: 12px;
    color: #7f8c8d;
}

.chat-time {
    font-size: 11px;
    color: #bdc3c7;
}

.unread-badge {
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    margin-left: 8px;
}

.bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #ecf0f1;
    display: flex;
    padding: 10px 0;
}

.nav-item {
    flex: 1;
    text-align: center;
    padding: 8px;
    font-size: 24px;
    cursor: pointer;
    transition: transform 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.nav-item:hover {
    transform: scale(1.1);
}

.nav-item.active {
    color: #667eea;
}

.nav-label {
    font-size: 10px;
    font-weight: 500;
}


/* Group Members View Styles */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 350px;
            position: relative;
        }
        
        .screen-label {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .chat-info {
            flex: 1;
        }
        
        .chat-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 2px;
        }
        
        .chat-status {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .chat-actions {
            display: flex;
            gap: 15px;
        }
        
        .chat-action {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .group-info-section {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #f1f3f4;
            text-align: center;
        }
        
        .group-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
        }
        
        .group-name-large {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .group-description {
            font-size: 14px;
            color: #95a5a6;
            line-height: 1.4;
            margin-bottom: 15px;
        }
        
        .group-stats {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 11px;
            color: #95a5a6;
            margin-top: 2px;
        }
        
        .action-buttons {
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            color: #2c3e50;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background: #f8f9fa;
        }
        
        .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        
        .members-container {
            flex: 1;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .members-section {
            background: white;
            margin-bottom: 10px;
        }
        
        .section-header {
            padding: 15px 20px 10px;
            background: white;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .section-count {
            font-size: 12px;
            color: #95a5a6;
        }
        
        .member-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            background: white;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.1s;
        }
        
        .member-item:hover {
            background: #f8f9fa;
        }
        
        .member-item:last-child {
            border-bottom: none;
        }
        
        .member-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            position: relative;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 15px;
            margin-bottom: 2px;
        }
        
        .member-role {
            font-size: 12px;
            color: #667eea;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .member-status {
            font-size: 12px;
            color: #95a5a6;
        }
        
        .online-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #2ecc71;
            border: 2px solid white;
            border-radius: 50%;
        }
        
        .member-actions {
            display: flex;
            gap: 10px;
        }
        
        .member-action-btn {
            background: none;
            border: none;
            color: #95a5a6;
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .member-action-btn:hover {
            background: #f1f3f4;
            color: #667eea;
        }
        
        .admin-badge {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .founder-badge {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .add-member-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.1s;
            color: #667eea;
        }
        
        .add-member-item:hover {
            background: #f8f9ff;
        }
        
        .add-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .add-text {
            font-weight: 600;
            font-size: 15px;
        }
    </style>
</head>
<body>
<div class="container">
<h1>💬 Chat Conversation Mockups</h1>
<div class="mockups-grid"><div class="phone-mockup">
<div class="screen-label">CHAT</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="app-header">
<div class="header-title">💬 Village Chat</div>
<button class="header-action">New</button>
</div>
<div class="content">
<input class="search-bar" placeholder="🔍 Search conversations..." type="text"/>
<div class="chat-list">
<div class="chat-item">
<div class="avatar">🏘️</div>
<div class="chat-content">
<div class="chat-name">Village Announcements</div>
<div class="chat-preview">New recycling schedule posted</div>
</div>
<div>
<div class="chat-time">9:15 AM</div>
<div class="unread-badge">2</div>
</div>
</div>
<div class="chat-item">
<div class="avatar">M</div>
<div class="chat-content">
<div class="chat-name">Maria Rodriguez</div>
<div class="chat-preview">Thanks for helping with the garden!</div>
</div>
<div class="chat-time">8:30 AM</div>
</div>
<div class="chat-item">
<div class="avatar">👨‍👩‍👧‍👦</div>
<div class="chat-content">
<div class="chat-name">Parents Group</div>
<div class="chat-preview">Sarah: Soccer practice moved to 4 PM</div>
</div>
<div>
<div class="chat-time">7:45 AM</div>
<div class="unread-badge">5</div>
</div>
</div>
<div class="chat-item">
<div class="avatar">J</div>
<div class="chat-content">
<div class="chat-name">John Smith</div>
<div class="chat-preview">The handyman job is still available</div>
</div>
<div class="chat-time">Yesterday</div>
</div>
<div class="chat-item">
<div class="avatar">🛒</div>
<div class="chat-content">
<div class="chat-name">Local Market Updates</div>
<div class="chat-preview">Fresh strawberries arrived today!</div>
</div>
<div>
<div class="chat-time">Yesterday</div>
<div class="unread-badge">1</div>
</div>
</div>
<div class="chat-item">
<div class="avatar">A</div>
<div class="chat-content">
<div class="chat-name">Anna Wilson</div>
<div class="chat-preview">Found your lost cat near the park</div>
</div>
<div class="chat-time">Yesterday</div>
</div>
<div class="chat-item">
<div class="avatar">🎪</div>
<div class="chat-content">
<div class="chat-name">Event Planning</div>
<div class="chat-preview">Mike: We need more volunteers for...</div>
</div>
<div class="chat-time">2 days ago</div>
</div>
</div>
</div>
<div class="bottom-nav">
<div class="nav-item">
<div>🏠</div>
<div class="nav-label">Home</div>
</div>
<div class="nav-item">
<div>👥</div>
<div class="nav-label">People</div>
</div>
<div class="nav-item active">
<div>💬</div>
<div class="nav-label">Chat</div>
</div>
<div class="nav-item">
<div>⚙️</div>
<div class="nav-label">More</div>
</div>
</div>
</div>
</div>
<!-- Create Group Screen -->
<div class="phone-mockup">
<div class="screen-label">CREATE GROUP</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="chat-header">
<button class="back-btn">←</button>
<div class="chat-info">
<div class="chat-name">New Group</div>
<div class="chat-status">Step 1 of 2</div>
</div>
<div class="chat-actions">
<button class="chat-action" style="color: #667eea; font-weight: bold;">Next</button>
</div>
</div>
<div class="messages-container" style="padding: 20px;">
<div style="text-align: center; margin-bottom: 30px;">
<div class="photo-upload">📷</div>
<div class="photo-upload-label">Add group photo</div>
</div>
<div class="form-group">
<label class="form-label">Group Name *</label>
<input class="form-input" placeholder="Enter group name..." type="text" value="Weekend Soccer Parents"/>
</div>
<div class="form-group">
<label class="form-label">Description</label>
<textarea class="form-textarea" placeholder="What's this group about?">Coordination for weekend soccer activities and carpools</textarea>
</div>
<div class="form-group">
<label class="form-label">Group Type</label>
<div class="group-type-selector">
<div class="group-type-option selected">
<div class="icon">👥</div>
<div class="label">Private</div>
</div>
<div class="group-type-option">
<div class="icon">🌐</div>
<div class="label">Public</div>
</div>
</div>
</div>
<div class="tip-box">
<div class="tip-title">💡 Tip</div>
<div class="tip-content">Private groups are only visible to members you invite. Public groups can be discovered by other community members.</div>
</div>
</div>
</div>
</div>
<!-- User Selection Screen -->
<div class="phone-mockup">
<div class="screen-label">SELECT MEMBERS</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="chat-header">
<button class="back-btn">←</button>
<div class="chat-info">
<div class="chat-name">Add Members</div>
<div class="chat-status">5 selected</div>
</div>
<div class="chat-actions">
<button class="chat-action" style="color: #667eea; font-weight: bold;">Create</button>
</div>
</div>
<div class="search-container">
<input class="search-input" placeholder="Search contacts..." type="text"/>
<div class="search-icon">🔍</div>
</div>
<div class="selected-members">
<div class="selected-title">SELECTED MEMBERS</div>
<div class="selected-chips">
<div class="selected-chip">
<div class="chip-avatar" style="background: #e74c3c;">S</div>
                                Sarah J.
                                <button class="chip-remove">×</button>
</div>
<div class="selected-chip">
<div class="chip-avatar" style="background: #3498db;">M</div>
                                Mike D.
                                <button class="chip-remove">×</button>
</div>
<div class="selected-chip">
<div class="chip-avatar" style="background: #2ecc71;">A</div>
                                Anna W.
                                <button class="chip-remove">×</button>
</div>
</div>
</div>
<div class="contacts-list">
<div class="contacts-section">
<div class="contacts-title">CONTACTS</div>
<div class="contact-item">
<div class="contact-avatar" style="background: #9b59b6;">L</div>
<div class="contact-info">
<div class="contact-name">Lisa Thompson</div>
<div class="contact-status">Online • Parent coordinator</div>
</div>
<div class="contact-checkbox selected">
<div class="checkbox-inner"></div>
</div>
</div>
<div class="contact-item">
<div class="contact-avatar" style="background: #f39c12;">J</div>
<div class="contact-info">
<div class="contact-name">John Martinez</div>
<div class="contact-status">Active 2h ago • Coach assistant</div>
</div>
<div class="contact-checkbox"></div>
</div>
<div class="contact-item">
<div class="contact-avatar" style="background: #e67e22;">K</div>
<div class="contact-info">
<div class="contact-name">Katie Brown</div>
<div class="contact-status">Online • Team mom</div>
</div>
<div class="contact-checkbox selected">
<div class="checkbox-inner"></div>
</div>
</div>
<div class="contact-item">
<div class="contact-avatar" style="background: #27ae60;">R</div>
<div class="contact-info">
<div class="contact-name">Robert Wilson</div>
<div class="contact-status">Active 1h ago • Parent</div>
</div>
<div class="contact-checkbox"></div>
</div>
<div class="contact-item">
<div class="contact-avatar" style="background: #8e44ad;">E</div>
<div class="contact-info">
<div class="contact-name">Emma Garcia</div>
<div class="contact-status">Online • Volunteer coordinator</div>
</div>
<div class="contact-checkbox selected">
<div class="checkbox-inner"></div>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Individual Chat -->
<div class="phone-mockup">
<div class="screen-label">INDIVIDUAL CHAT</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="chat-header">
<button class="back-btn">←</button>
<div class="chat-avatar">M</div>
<div class="chat-info">
<div class="chat-name">Maria Rodriguez</div>
<div class="chat-status">Active now</div>
</div>
<div class="chat-actions">
<button class="chat-action">📞</button>
<button class="chat-action">⋯</button>
</div>
</div>
<div class="messages-container">
<div class="system-message">Today</div>
<div class="message received">
<div class="message-bubble">
                                Hi! I saw your post about needing help with gardening. I'd love to help out! 🌱
                            </div>
<div class="message-time">9:15 AM</div>
</div>
<div class="message sent">
<div class="message-bubble">
                                That's wonderful! When would be a good time for you?
                            </div>
<div class="message-time">9:16 AM</div>
</div>
<div class="message received">
<div class="message-bubble">
                                I'm free this weekend, either Saturday or Sunday morning works for me.
                            </div>
<div class="message-time">9:17 AM</div>
</div>
<div class="message sent">
<div class="message-bubble">
                                Perfect! Saturday at 10 AM would be great. I'll have all the tools ready 🔧
                            </div>
<div class="message-time">9:18 AM</div>
</div>
<div class="message received">
<div class="message-bubble">
                                Sounds great! See you then. My address is 123 Oak Street.
                            </div>
<div class="message-time">9:19 AM</div>
</div>
<div class="typing-indicator">
<span>Maria is typing</span>
<div class="typing-dots">
<div class="typing-dot"></div>
<div class="typing-dot"></div>
<div class="typing-dot"></div>
</div>
</div>
</div>
<div class="message-input-container">
<input class="message-input" placeholder="Type a message..." type="text"/>
<button class="send-btn">➤</button>
</div>
</div>
</div>
<!-- Group Chat -->
<div class="phone-mockup">
<div class="screen-label">GROUP CHAT</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="chat-header">
<button class="back-btn">←</button>
<div class="chat-avatar group-avatar">👨‍👩‍👧‍👦</div>
<div class="chat-info">
<div class="chat-name">Parents Group</div>
<div class="chat-status">8 members</div>
</div>
<div class="chat-actions">
<button class="chat-action">📞</button>
<button class="chat-action">⋯</button>
</div>
</div>
<div class="messages-container">
<div class="system-message">Yesterday</div>
<div class="message received">
<div class="message-bubble">
<strong>Sarah Johnson:</strong> Soccer practice has been moved to 4 PM this Thursday due to field maintenance.
                            </div>
<div class="message-time">Yesterday 3:45 PM</div>
</div>
<div class="message received">
<div class="message-bubble">
<strong>Mike Davis:</strong> Thanks for the update! Will Tommy still need a ride?
                            </div>
<div class="message-time">Yesterday 3:47 PM</div>
</div>
<div class="system-message">Today</div>
<div class="message received">
<div class="message-bubble">
<strong>Anna Wilson:</strong> Yes, I can still pick up Tommy and Jake if needed 🚗
                            </div>
<div class="message-time">7:30 AM</div>
</div>
<div class="message sent">
<div class="message-bubble">
                                That would be amazing, thank you Anna! 🙏
                            </div>
<div class="message-time">7:32 AM</div>
</div>
<div class="message received">
<div class="message-bubble">
<strong>Lisa Thompson:</strong> Don't forget about the bake sale next Friday! We still need volunteers.
                            </div>
<div class="message-time">8:15 AM</div>
<div class="quick-replies">
<div class="quick-reply">👍 I can help</div>
<div class="quick-reply">🧁 I'll bake</div>
<div class="quick-reply">❓ What time?</div>
</div>
</div>
</div>
<div class="message-input-container">
<input class="message-input" placeholder="Type a message..." type="text"/>
<button class="send-btn">➤</button>
</div>
</div>
</div>
<!-- Community Announcements -->
<div class="phone-mockup">
<div class="screen-label">GROUP MEMBERS</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="chat-header">
<button class="back-btn">←</button>
<div class="chat-info">
<div class="chat-name">Group Info</div>
<div class="chat-status">Parents Group</div>
</div>
<div class="chat-actions">
<button class="chat-action">⋯</button>
</div>
</div>
<div class="group-info-section">
<div class="group-avatar-large">👨‍👩‍👧‍👦</div>
<div class="group-name-large">Parents Group</div>
<div class="group-description">
                    Coordination for weekend soccer activities and carpools. Stay updated on practice schedules and events.
                </div>
<div class="group-stats">
<div class="stat-item">
<div class="stat-number">8</div>
<div class="stat-label">Members</div>
</div>
<div class="stat-item">
<div class="stat-number">245</div>
<div class="stat-label">Messages</div>
</div>
<div class="stat-item">
<div class="stat-number">12</div>
<div class="stat-label">Days old</div>
</div>
</div>
</div>
<div class="action-buttons">
<button class="action-btn">🔇 Mute</button>
<button class="action-btn primary">📱 Add Contact</button>
<button class="action-btn">📤 Share</button>
</div>
<div class="members-container">
<div class="members-section">
<div class="section-header">
<div class="section-title">Admins</div>
<div class="section-count">2</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #e74c3c;">
                            S
                            <div class="online-indicator"></div>
</div>
<div class="member-info">
<div class="member-name">
                                Sarah Johnson
                                <span class="founder-badge">FOUNDER</span>
</div>
<div class="member-role">Group Admin</div>
<div class="member-status">Active now</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #3498db;">
                            M
                        </div>
<div class="member-info">
<div class="member-name">
                                Mike Davis
                                <span class="admin-badge">ADMIN</span>
</div>
<div class="member-role">Co-Admin</div>
<div class="member-status">Active 2h ago</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
</div>
<div class="members-section">
<div class="section-header">
<div class="section-title">Members</div>
<div class="section-count">6</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #2ecc71;">
                            A
                            <div class="online-indicator"></div>
</div>
<div class="member-info">
<div class="member-name">Anna Wilson</div>
<div class="member-role">Parent coordinator</div>
<div class="member-status">Active now</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #9b59b6;">
                            L
                            <div class="online-indicator"></div>
</div>
<div class="member-info">
<div class="member-name">Lisa Thompson</div>
<div class="member-role">Team mom</div>
<div class="member-status">Active now</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #f39c12;">
                            J
                        </div>
<div class="member-info">
<div class="member-name">John Martinez</div>
<div class="member-role">Coach assistant</div>
<div class="member-status">Active 1h ago</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #e67e22;">
                            K
                        </div>
<div class="member-info">
<div class="member-name">Katie Brown</div>
<div class="member-role">Volunteer coordinator</div>
<div class="member-status">Active 3h ago</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #27ae60;">
                            R
                        </div>
<div class="member-info">
<div class="member-name">Robert Wilson</div>
<div class="member-role">Parent</div>
<div class="member-status">Active 5h ago</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="member-item">
<div class="member-avatar" style="background: #8e44ad;">
                            E
                            <div class="online-indicator"></div>
</div>
<div class="member-info">
<div class="member-name">Emma Garcia</div>
<div class="member-role">Parent</div>
<div class="member-status">Active now</div>
</div>
<div class="member-actions">
<button class="member-action-btn">💬</button>
<button class="member-action-btn">📞</button>
</div>
</div>
<div class="add-member-item">
<div class="add-icon">+</div>
<div class="add-text">Add members</div>
</div>
</div>
</div>
</div>
</div></div></div></body></html>